package com.project.business.config.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.MessageEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class RedBody implements Serializable {
    
    private static final long serialVersionUID = 1279554190232779043L;
    /**
     * 消息发起人
     */
    @JSONField(name = "UserID")
    private Long userId;
    private String nickName;
    private String iconUrl;
    private String msgType;
    private RedEnvelopeDTO msgBody;
    
    public RedBody() {
        this.msgType = MessageEnum.SHARE.getClassName();
    }
}
