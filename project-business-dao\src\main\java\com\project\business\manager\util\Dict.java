package com.project.business.manager.util;

public enum Dict {
    ROLE_TYPE("ROLE_TYPE","角色类型"),
    GAME_TYPE("GAME_TYPE","游戏类型"),
    GAME_STATUS("GAME_STATUS","游戏状态"),
    BBS_TYPE("BBS_TYPE","公告类型"),
    BBS_STATUS("BBS_STATUS","公告状态"),
    BBS_SEND_STATUS("BBS_SEND_STATUS","公告发送状态"),
    EMAIL_STATUS("EMAIL_STATUS","邮件发送状态");


    private String code;
    private String name;

    Dict(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}