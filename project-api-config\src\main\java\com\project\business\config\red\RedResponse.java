package com.project.business.config.red;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class RedResponse implements Serializable {
    
    private static final long serialVersionUID = -2491286325754330453L;
    @JSONField(defaultValue = "200成功 非200错误")
    private Long code;
    @JSONField(defaultValue = "错误或者成功信息")
    private String message;
}
