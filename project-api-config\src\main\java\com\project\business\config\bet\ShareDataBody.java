package com.project.business.config.bet;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.MessageEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class ShareDataBody implements Serializable {
    
    private static final long serialVersionUID = 4337259430615843320L;
    /**
     * 消息发起人
     */
    @JSONField(name = "UserID")
    private Long userId;
    private String nickName;
    private String iconUrl;
    private String msgType;
    private String msgStyle;
    private MessageContent msgBody;
    
    public ShareDataBody() {
        this.msgType = MessageEnum.SHARE.getClassName();
    }
}
