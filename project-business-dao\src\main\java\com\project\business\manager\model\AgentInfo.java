package com.project.business.manager.model;

import com.project.framework.database.base.BaseModel;
import lombok.Getter;
import lombok.Setter;


/**
 * 代理基础信息配置
 * 
 *
 */
@Getter
@Setter
public class AgentInfo extends BaseModel{

	private static final long serialVersionUID = -7486014736010155735L;
	/**
	 * 平台名称
	 */
	private String platName;

	/**
	 * 平台种类 1:平台 2 代理 
	 */
	private Integer platClass=2;

	/**
	 * 代理ID
	 */
	private Long parentPlat;

	/**
	 * 域名
	 */
	private String domainUrl;

	/**
	 * 联系邮箱
	 */
	private String platEmail;

	/**
	 * 联系电话
	 */
	private String platPhone;

	/**
	 * IP白名单
	 */
	private String whiteIp;

}
