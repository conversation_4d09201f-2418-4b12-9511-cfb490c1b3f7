package com.project.business.api.controller.jpush;

import com.project.business.service.message.JPushService;
import com.project.business.service.message.vo.JPushVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/apiIm/chat")
public class JPushController {

    @Autowired
    private JPushService jPushService;

    @PostMapping("/pushMessage")
    public Object pushMessage(HttpServletRequest request, HttpServletResponse response, @Valid @RequestBody JPushVo reqVo){
        jPushService.pushMessageToOne(reqVo);
        return null;
    }
}
