package com.project.business.api.service.vo;

import com.project.framework.core.base.BaseVo;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class OpenBalanceVo extends BaseVo {
    
    private static final long serialVersionUID = -2533729666963176761L;
    @NotNull(message = "vo.parentPlat")
    private Long parentPlat;
    @NotNull(message = "vo.platId")
    private Long platId;
    @NotNull(message = "vo.userId")
    private Long userId;
    @NotBlank(message = "vo.sessionId")
    private String sessionId;
    private String domain;
    private String method;
}
