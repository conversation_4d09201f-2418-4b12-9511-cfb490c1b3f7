package com.project.business.api.filter;

import com.alibaba.fastjson.JSON;
import com.project.business.api.util.ApiThreadLocal;
import com.project.business.service.token.TokenService;
import com.project.framework.core.RespObject;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@Component
public class TokenInterceptor extends HandlerInterceptorAdapter {

    private final Logger log = LoggerFactory.getLogger(TokenInterceptor.class);
    @Autowired
    private TokenService tokenService;

    private static final List<String> WHITE_LIST = Arrays.asList(
            "/apiChat/live/callbacks"
    );


    @Override
    public boolean preHandle(HttpServletRequest httpRequest, HttpServletResponse httpResponse, Object handler)
            throws Exception {

        String method = httpRequest.getMethod();
        if (method.equalsIgnoreCase("OPTIONS")) {
            httpResponse.setStatus(200);
            httpResponse.getWriter().write(JSON.toJSONString(new RespObject()));
            return false;
        }
        String requestUri = httpRequest.getRequestURI();
        log.info("请求URL::" + httpRequest.getRequestURL().toString());

        if (isWhiteListed(requestUri)) {
            return true;
        }


        final String OAuthToken = httpRequest.getHeader("Authorization");
        log.info("请求URL::" + httpRequest.getRequestURL().toString());
        log.info("USER_TOKEN::" + OAuthToken);
        if (StringUtils.isEmpty(OAuthToken)) {
            throw new GlobalException(Cause.error_user_token);
        }
        ApiThreadLocal.set(tokenService.checkToken(OAuthToken));
        return super.preHandle(httpRequest, httpResponse, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        ApiThreadLocal.remove();
        super.afterCompletion(request, response, handler, ex);
    }
    private boolean isWhiteListed(String requestUri) {
        return WHITE_LIST.stream().anyMatch(requestUri::startsWith);
    }
}