package com.project.business.config.reward;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


@Setter
@Getter
public class RewardResponse implements Serializable {


    private static final long serialVersionUID = -4071415774556652872L;
    @JSONField(defaultValue = "200成功 非200错误")
    private Long code;
    @JSONField(defaultValue = "错误或者成功信息")
    private String message;
}

