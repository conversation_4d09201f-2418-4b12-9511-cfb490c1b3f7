package com.project.business.api.controller.merchant;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.merchant.MerchantService;
import com.project.business.service.user.vo.ThirdLoginVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import com.project.framework.core.exception.Language;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redis.RedisKeyPrefix;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiChat")
public class MerchantController {

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private RedisClient redisClient;

    @PostMapping("/topUpLogin")
    public Object topUpLogin(HttpServletRequest request, HttpServletResponse response,
                             @RequestBody @Valid ThirdLoginVo reqVo){
        final String key = RedisKeyConstant.PLAT_ID + reqVo.getLoginToken();
        if (!redisClient.exists(RedisKeyPrefix.BUSINESS, key)){
            throw new GlobalException(Cause.error_third_user_token);
        }
        Long platId = (Long) redisClient.get(RedisKeyPrefix.BUSINESS, key);
        ApiThreadLocal.set(new TokenMsg(null, platId, null, null, null));
        return merchantService.topUpLogin(reqVo);
    }

}
