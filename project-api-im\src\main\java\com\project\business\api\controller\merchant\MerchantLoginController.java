package com.project.business.api.controller.merchant;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.service.bean.MerchantTokenMsg;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.merchant.MerchantLoginService;
import com.project.business.service.merchant.vo.MerchantCheckVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.project.business.service.token.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiIm")
public class MerchantLoginController {
    
    @Autowired
    private MerchantLoginService merchantLoginService;
    @Autowired
    private TokenService tokenService;
    
    @PostMapping("/merchantCheck")
    public Object merchantCheck(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid MerchantCheckVo reqVo) throws Exception {
        MerchantTokenMsg token = tokenService.checkMerchantToken(reqVo.getAuthorization());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,token.getLanguage()));
        return merchantLoginService.merchantCheck(reqVo);
    }
}


