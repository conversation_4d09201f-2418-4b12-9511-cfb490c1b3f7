spring:
  application:
    name: api<PERSON>hat
  redis:
    data-base: 2
    host: redis-java
    port: 6379
    password: Jsl#dsu973yhd
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ******************************************************************************************************************************************* # 数据库地址
    username: dev_user # 数据库用户名
    password: dev_password # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver # 数据库驱动
    type: com.zaxxer.hikari.HikariDataSource
    minimum-idle: 5
    maximum-pool-size: 200
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 100000
    connection-timeout: 10000
    connection-test-query: SELECT 1
    time-between-eviction-runs-millis: 300000
server:
  port: 9100
redisson: # 分布式事物锁redis配置
  address: redis://redis-java:6379
  data-base: 2
  password: Jsl#dsu973yhd
  timeout: 30000
security:
  auth-url: /**
  ignore-url : /components/**, /css/**, /js/**, /image/**

sts:
  endpoint: sts.ap-southeast-1.aliyuncs.com
  accessKeyId: LTAI5tKuWG8pcRyXTsfEbBiq
  accessKeySecret: ******************************
  roleArn: acs:ram::5719026860824638:role/chat-img
  roleSessionName: chat-ceshi
  policy: config/policy.json
  path: /securityChat

im:
  url: http://go-api-chat:8090/
  apiBalanceUrl: http://nginx-private/apiBalance/

tencent:
  translate:
    secret-id: AKIDhw2d0BBHV5VkFVvWCG3buwilmYhe9ZXE
    secret-key: bEpb9Rt7BD46LMKCpBLJ0RedkbBUERO4
    endpoint: tmt.tencentcloudapi.com
    region: ap-hongkong
  sms:
    secret-id: AKIDzoOjoc9PO7ZlhNmtjIbeRxB8oDLCn8HA
    secret-key: cx1pX5OTIrAE2cw0pOhkY6OPAk49zZu1
third:
  company:
    privateKey: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAKSLG0h7QzMIUNbd4INKr/pIUU354r7ODWrrZJdNsmmu3vM4PoP0DzNTV9MTNj37SBVRiY4w84XyMjwQxk6eXaMNS2fcp0PZS557f9yYR8ykA374owbpN+bencjPiTD90uYzUQCQAMinjMWaS/F38E352ZP/VOnzuYxRAFLya91HAgMBAAECgYBXJCSq2jzYhgrqAE+quJ9CGZKVjUV8b3vDqC55wvg96BLDFIJnN5XlRLo6Wu5bHP0r7SbAW0RdJ8ta7DdZ450Kow+k2Z69gYO818cptQXYrs4ky6M48NXeSSoYeGESxW7LGJs++o2nGmVRkhj4DMYY8lur1oYsyDAy/d3B0ucnwQJBAP0Kc2KCOl8xnbXUuoFhJHaVoKPWqdhody5sNHK+11Bgc3/ZhqNMT1uIiiZnB3CTyfKeJAgX0fwde7fmtZHaUO8CQQCmd7a3qXvUbnQao03ITrthGRvGAJSAfTAG/VEx2g1knxUmiq+bek+FGi7UYXYRZ/rVqX934ztTAOnBqVtnK4kpAkEAs6KAqVUkFUJG4LfFM2YAGcM9SDJzXvNCcI1WaoM6nY/rTr7hCvp4d9WlpX+M04nHWtqTX79xTdasZrB9A68FtwJAHXWmIk6eGXQKnAQ2abJ1OrPE1H+ZyDtfWn1N9zKNmDcG+TEl7q/wjq+ZhgBRcrciDtnWMxNFlmTc+WbNRC7SMQJBAIBSE1kfhy3V63db91Gnr789G2QKy3TI46QEY3dirLiXWF3/tt+9K8itjeB22S5S7cWbzJ+2FIFWmFB/DP3ER8Q=
