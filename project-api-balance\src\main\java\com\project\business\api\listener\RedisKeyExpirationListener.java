package com.project.business.api.listener;

import com.project.business.api.service.PlatUserBalanceService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {
    
    private final static Logger logger = LoggerFactory.getLogger(RedisKeyExpirationListener.class);
    @Autowired
    private PlatUserBalanceService platUserBalanceService;
    
    public RedisKeyExpirationListener(RedisMessageListenerContainer redisContainer) {
        super(redisContainer);
    }
    
    @Override
    public void onMessage(Message message, byte[] pattern) {
        String expiredKey = message.toString();

        String[] keyArr = StringUtils.split(expiredKey, ":");
        if (ArrayUtils.indexOf(keyArr, "{QUARTZ_PLAT_BALANCE}") != -1) {
            logger.info("======过期key=====" + expiredKey);
            //余额监听
            String userId = keyArr[keyArr.length - 1];
            platUserBalanceService.runMessage(userId);
        }
        
    }
}
