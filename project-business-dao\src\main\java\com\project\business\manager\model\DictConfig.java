package com.project.business.manager.model;

import com.alibaba.fastjson.JSON;
import com.project.framework.database.base.BaseModel;
import com.project.business.manager.dto.DictContent;
import com.project.business.manager.util.Dict;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 
 * <br>数据字典配置表
 * <b>功能：</b>DictConfigEntity<br>
 * <AUTHOR>
 * @version 1.0
 * @date ${date}
 */
@Getter
@Setter
public class DictConfig extends BaseModel {


	private static final long serialVersionUID = -4501377385350494120L;
	//字典编码
	private String dictCode;
	//字典内容
	private String dictContent;
	//字典名称
	private String dictName;

	public DictConfig(){

	}

	public DictConfig(Dict dict, List<DictContent> dictList){
		super();
		this.dictCode=dict.getCode();
		this.dictName=dict.getName();
		this.dictContent= JSON.toJSONString(dictList);
	}
}

