package com.project.business.config.bet;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.MessageEnum;
import com.project.framework.core.base.BaseVo;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class GroupNoticeVo extends BaseVo {
    
    private static final long serialVersionUID = -1068757617384161692L;
    @NotEmpty(message = "vo.groupList")
    private List<PlaceBase> groupList;
    @NotBlank(message = "vo.companyId")
    private String companyId;
    @JSONField(defaultValue = "")
    private String userId;
    @NotBlank(message = "vo.msgType")
    private String msgType;
    @JSONField(defaultValue = "")
    private String msgStyle;
    @NotNull(message = "vo.msgBody")
    private MsgBody msgBody;
    /**
     * 1为个人消息, 2为群组消息 当此字段为1时 , userId不能为空
     */
    @NotBlank(message = "vo.msgPrivate")
    private Integer msgPrivate;
    private MessageEnum gameType;
    /**
     * 是否开启替换值 , 1不开启   2开启
     */
    private Integer replaceKey;
}
