package com.project.business.api.controller.user;

import com.alibaba.fastjson.JSON;
import com.project.business.customer.dao.LiveRecordDao;
import com.project.business.customer.dto.LiveRecordDTO;
import com.project.business.service.game.EventConfigService;
import com.project.business.service.game.vo.RoomRecordDTO;
import com.project.business.service.user.UserExtendService;
import com.project.business.service.user.UserService;
import com.project.business.service.user.vo.GroupNickNameVo;
import com.project.business.service.user.vo.UserIdVo;
import com.project.framework.core.code.Result;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redisson.RedissLockUtil;
import lombok.Getter;
import lombok.Setter;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/apiIm")
public class UserController {

    @Autowired
    private UserExtendService extendService;
    @Autowired
    private UserService userService;
    @Autowired
    private EventConfigService eventConfigService;
    @Autowired
    private LiveRecordDao liveRecordDao;
    @Resource
    private RedisClient redisClient;
    private final Logger log = LoggerFactory.getLogger(UserController.class);


    @PostMapping("/queryUserInfo")
    public Object queryUserInfo(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid UserIdVo reqVo) {
        return new Result(userService.queryUserInfo(reqVo.getUserId()));
    }

    @PostMapping("/queryLoginUserInfo")
    public Object queryLoginUserInfo(HttpServletRequest request, HttpServletResponse response) {
        return userService.queryLoginUserInfo();
    }

    @PostMapping("/checkUser")
    public Object checkUserById(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid UserIdVo reqVo) {
        return userService.checkUserById(reqVo.getUserId());
    }

    @PostMapping("/queryGroupByUser")
    public Object queryGroupByUser(HttpServletRequest request, HttpServletResponse response) {
        return new Result(extendService.queryGroupListByUserId());
    }

    @PostMapping("/updateGroupNickName")
    public Object updateGroupNickName(HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody @Valid GroupNickNameVo reqVo) {
        userService.updateGroupNickName(reqVo);
        return null;
    }

    @PostMapping("/exitRoom")
    public Object exitRoom(HttpServletRequest request, HttpServletResponse response, @RequestBody RoomRecordDTO prizevVo
    ) {
//        String key = RedisKeyConstant.CHATROOM_ONLINE_STATS + prizevVo.getPlatId() + ":" + prizevVo.getPlaceId();
        String key = "CHATROOM_ONLINE_STATS" + prizevVo.getPlatId();
        RLock rlock = RedissLockUtil.multiLock(10, key);
        try {
            return eventConfigService.exitRoom2(prizevVo);
        } finally {
            rlock.unlock();
        }
    }

    @PostMapping("/queryLiveInfo")
    public Object queryLiveInfo(HttpServletRequest request, HttpServletResponse response, @RequestBody queryLiveInfoDto prizevVo
    ) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        stringObjectHashMap.put("platId", prizevVo.getPlatId());
        stringObjectHashMap.put("status", 1);
        List<LiveRecordDTO> objects = liveRecordDao.queryByList2(stringObjectHashMap);
        for (LiveRecordDTO object : objects) {
            Integer resourceFlag = object.getResourceFlag();
            if (resourceFlag == 1) {
                object.setPullUrl(object.getOutsideUrl());
            }
        }
        log.info("queryLiveInfo------------------------------::" + JSON.toJSONString(objects));
        return objects;
    }

    @PostMapping("/queryLiveNumByRoomId")
    public Object queryLiveNumByRoomId(HttpServletRequest request, HttpServletResponse response, @RequestBody queryLiveNumByRoomIdDto prizevVo
    ) {
        return eventConfigService.exitRoom3(prizevVo.getPullUrl());
    }

    @PostMapping("/liveCountUsers")
    public Object liveCountUsers(HttpServletRequest request, HttpServletResponse response, @RequestBody LiveCountUsersDto prizevVo
    ) {
        log.info("liveCountUsers------------------------------::" + JSON.toJSONString(prizevVo));
        Long platId = prizevVo.getPlatID();
        Long userID = prizevVo.getUserID();
        Long id = prizevVo.getLiveID();
        Long actionType = prizevVo.getActionType();
        Long chatRoomID = prizevVo.getChatRoomID();

        String lockString = "liveCountUsers" + platId.toString() + chatRoomID.toString() + id.toString();
        RLock rlock = RedissLockUtil.multiLock(10, lockString);
        try {
            String key = RedisKeyConstant.LIVE_MAX_ONLINE_COUNT_SET + platId + ":" + chatRoomID + ":" + id;
            String onLineCount = RedisKeyConstant.LIVE_TOTAL_ONLINE_COUNT + platId + ":" + chatRoomID + ":" + id;
            String maxCount = RedisKeyConstant.LIVE_MAX_ONLINE_COUNT + platId + ":" + chatRoomID + ":" + id;
            redisClient.addToSet(onLineCount, userID);
            if (actionType == 1) {
                redisClient.addToSet(key, userID);
                long setSize = redisClient.getSetSize(key) - 1;
                Long counterValue = redisClient.getCounter(maxCount);
                if (setSize > counterValue) {
                    redisClient.setValue(maxCount, setSize);
                }
            } else {
                redisClient.removeFromSet(key, userID);
            }
        } finally {
            rlock.unlock();
        }
        return true;
    }

    @Getter
    @Setter
    static class queryLiveInfoDto {
        private Long platId;
    }

    @Getter
    @Setter
    static class queryLiveNumByRoomIdDto {
        private String pullUrl;
    }

    @Getter
    @Setter
    static class LiveCountUsersDto {
        private Long platID;
        private Long userID;
        private Long liveID;
        private Long chatRoomID;
        private Long actionType;
    }


}
