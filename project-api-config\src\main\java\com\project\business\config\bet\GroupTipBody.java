package com.project.business.config.bet;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class GroupTipBody implements Serializable {
    
    private static final long serialVersionUID = -886888835044358374L;
    /**
     * 跟投人
     */
    @NotBlank(message = "vo.userId")
    private String userId;
    /**
     * 场景，例如：“重庆时时彩”
     */
    @NotBlank(message = "vo.scenes")
    private String scenes;
    /**
     * 金额，例如：“3.00元”
     */
    @NotBlank(message = "vo.amount")
    private String amount;
    /**
     * 主体信息, 例如: "用户{userId}跟投{scenes}：{amount}"
     */
    @NotBlank(message = "vo.subject")
    private String subject;
}
