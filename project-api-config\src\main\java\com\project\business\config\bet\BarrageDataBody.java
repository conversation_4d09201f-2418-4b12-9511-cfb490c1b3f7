package com.project.business.config.bet;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.MessageEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class BarrageDataBody implements Serializable {
    
    private static final long serialVersionUID = 601398451322224826L;
    /**
     * 消息发起人
     */
    @JSONField(name = "UserID")
    private Long userId;
    private String nickName;
    private String iconUrl;
    private String msgType;
    private String msgStyle;
    private BarrageMessage[] msgBody;
    @JSONField(defaultValue = "3")
    private Integer pollingCount;
    @JSONField(defaultValue = "10000")
    private Integer periodMillSecond;
    
    public BarrageDataBody() {
        this.msgType = MessageEnum.BARRAGE.getClassName();
    }
}
