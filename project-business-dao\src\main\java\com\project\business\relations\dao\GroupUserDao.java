package com.project.business.relations.dao;

import com.alibaba.fastjson.JSON;
import com.project.business.relations.model.GroupUser;
import com.project.framework.core.base.PageVo;
import com.project.framework.core.code.PageResult;
import com.project.framework.database.base.BaseDao;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 群组用户信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-02 13:45:11
 */
public interface GroupUserDao extends BaseDao {

    int batchAdd(List<GroupUser> userList);

    int updateRoleId(GroupUser groupUser);

    int deleteByGroupId(Long groupId);

    int createTemporaryTable(Map<String, Object> param);

    long queryByCount1(Map<String, Object> params);

    @SuppressWarnings({ "unchecked", "rawtypes" })
    default PageResult queryByPage1(PageVo reqVo) {
        Map<String, Object> params = (Map<String, Object>) JSON.toJSON(reqVo);
        final long count = queryByCount1(params);
        final List rowList = this.queryByListLimit(params);
        PageResult page = new PageResult(reqVo.getPageNumber(), reqVo.getPageSize(), count, rowList);
        return page;
    }

    /**
     * 查询群组管理员列表
     * 
     * @param groupId
     * @return
     */
    List<GroupUser> queryManagerByGroupId(@Param("groupId") Long groupId);

    /**
     * 修改群成员昵称
     * 
     * @param groupUser
     * @return
     */
    long updateGroupUserRemark(GroupUser groupUser);

    /**
     * 根据群组管理员查询群组的用户
     * 
     * @param groupId
     * @param roleId
     * @return
     */
    List<GroupUser> queryGroupUserByRoleId(@Param("groupId") Long groupId, @Param("roleId") Long roleId);

    <T> T queryByOne(Map<String, Object> reqVo);

}
