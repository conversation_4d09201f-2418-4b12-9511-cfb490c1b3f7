package com.project.business.api.controller.user;

import com.project.business.service.user.UserExtendService;
import com.project.business.service.user.UserService;
import com.project.business.service.user.vo.GroupNickNameVo;
import com.project.business.service.user.vo.ProhibitedWordsVo;
import com.project.business.service.user.vo.UserIdVo;
import com.project.framework.core.base.BaseVo;
import com.project.framework.core.code.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/apiChat")
public class UserController {
    
    @Autowired
    private UserExtendService extendService;
    @Autowired
    private UserService userService;
    
    @PostMapping("/queryUserInfo")
    public Object queryUserInfo(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid UserIdVo reqVo) {
        return new Result(userService.queryUserInfo(reqVo.getUserId()));
    }
    
    @PostMapping("/queryLoginUserInfo")
    public Object queryLoginUserInfo(HttpServletRequest request, HttpServletResponse response) {
        return userService.queryLoginUserInfo();
    }
    
    @PostMapping("/checkUser")
    public Object checkUserById(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid UserIdVo reqVo) {
        return userService.checkUserById(reqVo.getUserId());
    }
    
    @PostMapping("/queryGroupByUser")
    public Object queryGroupByUser(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody BaseVo baseVo) {
        return new Result(extendService.queryGroupListByUserId());
    }
    
    @PostMapping("/updateGroupNickName")
    public Object updateGroupNickName(HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody @Valid GroupNickNameVo reqVo) {
        userService.updateGroupNickName(reqVo);
        return null;
    }

    @PostMapping("/checkUserBind")
    public Object checkUserBind(HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> map = new HashMap<>();
        map.put("BindFlag",userService.checkUserBind());
        return map;
    }

    @PostMapping("/checkBanWords")
    public Object queryBannedWordslist(HttpServletRequest request, HttpServletResponse response , @RequestBody  ProhibitedWordsVo reqVo) {
        reqVo.setWordType(1);
        reqVo.setGroupId(0L);
        return userService.queryBannedWords(reqVo);
    }




}
