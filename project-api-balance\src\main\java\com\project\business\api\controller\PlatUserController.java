package com.project.business.api.controller;

import com.project.business.api.service.PlatUserBalanceService;
import com.project.business.api.service.vo.OpenBalanceVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiBalance")
public class PlatUserController {

    @Autowired
    private PlatUserBalanceService platUserBalanceService;

    @RequestMapping("/openBalance")
    public Object openBalance(HttpServletRequest request, HttpServletResponse response,
                              @RequestBody @Valid OpenBalanceVo reqVo) {
        return platUserBalanceService.openBalance(reqVo);
    }

    @RequestMapping("/startBalance")
    public Object startBalance(HttpServletRequest request, HttpServletResponse response,
                               @RequestBody @Valid OpenBalanceVo reqVo) {
        platUserBalanceService.startBalance(reqVo);
        return true;
    }

}
