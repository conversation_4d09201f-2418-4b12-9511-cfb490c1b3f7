package com.project.business.relations.model;

import com.project.framework.database.base.BaseModel;
import lombok.Getter;
import lombok.Setter;


/**
 * 好友关系表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-02 13:45:11
 */
@Getter
@Setter
public class UserFriend extends BaseModel{

	private static final long serialVersionUID = 5674285757035244742L;
	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 用户ID
	 */
	private Long friendId;

	/**
	 * 相互备注
	 */
	private String remarkUser;

	/**
	 * 相互备注
	 */
	private String remarkFriend;

	/**
	 * 关系类型
	 */
	private Integer status;

}
