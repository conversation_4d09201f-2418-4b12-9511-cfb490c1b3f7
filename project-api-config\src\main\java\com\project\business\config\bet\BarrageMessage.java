package com.project.business.config.bet;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class BarrageMessage implements Serializable {
    
    private static final long serialVersionUID = -1612860752851544617L;
    /**
     * 问候语，例如：“恭喜！XXX用户。”
     */
    @NotBlank(message = "vo.greeting")
    private String greetings;
    /**
     * 场景，例如：“重庆时时彩”
     */
    @NotBlank(message = "vo.scenes")
    private String scenes;
    /**
     * 动作，例如：“猜前二复制”
     */
    @NotBlank(message = "vo.action")
    private String action;
    /**
     * 结果，例如：“喜中32514”
     */
    @NotBlank(message = "vo.result")
    private String result;
}
