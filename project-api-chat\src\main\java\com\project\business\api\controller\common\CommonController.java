package com.project.business.api.controller.common;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.token.TokenService;
import com.project.framework.bean.StsResult;
import com.project.framework.bean.TranslateVo;
import com.project.framework.common.util.DateUtil;
import com.project.framework.core.base.BaseVo;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redis.RedisKeyPrefix;
import com.project.framework.redisson.RedissLockUtil;
import com.project.framework.service.StsSignService;
import com.project.framework.service.TranslateService;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.TimeUnit;


@RestController
@RequestMapping("/apiChat")
public class CommonController {
    
    @Autowired
    private StsSignService signService;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private TranslateService translateService;

    @PostMapping("/getStsSign")
    public Object getSTS(HttpServletRequest request, HttpServletResponse response) throws Exception {
        StsResult stsResult = null;
        String key = "lock_key_api";
        if (this.redisClient.exists(RedisKeyPrefix.BUSINESS, RedisKeyConstant.STS_TOKEN)) {
            stsResult = (StsResult)this.redisClient.get(RedisKeyPrefix.BUSINESS, RedisKeyConstant.STS_TOKEN);
        } else {
            RLock rlock = RedissLockUtil.multiLock(10, new String[] { key });
            try {
                if (rlock.tryLock(10L, TimeUnit.SECONDS)) {
                    try {
                        stsResult = this.signService.querySts();
                        if (stsResult != null) {
                            LocalDateTime localDateTime = DateUtil.dateToLocalTime(stsResult.getExpiration());
                            long ttlLong = localDateTime.toEpochSecond(ZoneOffset.of("+8")) - LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
                            int ttl = (int)Math.max(ttlLong, 1L);
                            this.redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.STS_TOKEN, stsResult, ttl);
                        }
                    } finally {
                        rlock.unlock();
                    }
                } else {
                    stsResult = (StsResult)this.redisClient.get(RedisKeyPrefix.BUSINESS, RedisKeyConstant.STS_TOKEN);
                }
            } catch (Exception e) {
                throw new GlobalException(Cause.error_sts_sign);
            }
        }
        if (stsResult == null)
            throw new GlobalException(Cause.error_sts_sign);
        LocalDateTime expiration = DateUtil.dateToLocalTime(stsResult.getExpiration());
        long se = expiration.toEpochSecond(ZoneOffset.of("+8"));
        long now = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        stsResult.setExpireSecond(Long.valueOf(se - now));
        return stsResult;
    }

    @PostMapping("/updateLanguage")
    public Object updateLanguage(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid BaseVo reqVo) throws Exception {
        if (reqVo == null || StringUtils.isBlank(reqVo.getLanguage())){
            throw new GlobalException(Cause.params_fail);
        }
        tokenService.updateChatUserToken(request,reqVo.getLanguage());
        TokenMsg tokenMsg = ApiThreadLocal.get();
        tokenMsg.setLanguage(reqVo.getLanguage());
        ApiThreadLocal.set(tokenMsg);
        return null;
    }

    @PostMapping("/translateText")
    public Object textTranslate(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid TranslateVo reqVo){
        return translateService.textTranslate(reqVo);
    }

    @RequestMapping("/apiTest")
    public Object apiTest(HttpServletRequest request, HttpServletResponse response)  {
        return null;
    }
}
