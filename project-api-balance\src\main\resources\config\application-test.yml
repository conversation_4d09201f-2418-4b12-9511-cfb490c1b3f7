spring:
  application:
    name: apiBalance
  redis:
    data-base: 3
    host: *************
    port: 6679
    password: <EMAIL>
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ***************************************************************************************************************************************************
    username: chat
    password: Abc123!@#.
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    minimum-idle: 5
    maximum-pool-size: 200
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 100000
    connection-timeout: 10000
    connection-test-query: SELECT 1
server:
  port: 9600
redisson:
  address: redis://*************:6679
  data-base: 2
  password: <EMAIL>
  timeout: 30000
im:
  url: http://*************/