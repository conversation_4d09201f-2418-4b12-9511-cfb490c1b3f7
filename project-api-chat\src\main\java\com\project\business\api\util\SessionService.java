package com.project.business.api.util;

import com.project.business.dict.UserFlag;
import com.project.business.service.bean.TokenMsg;
import com.project.framework.common.service.ISessionService;
import org.springframework.stereotype.Service;

@Service
public class SessionService implements ISessionService {
    
    @Override
    public Long getUserId() {
        return user().getUserId();
    }
    
    @Override
    public String getUserName() {
        return user().getUserId().toString();
    }
    
    @Override
    public void refresh() {
    
    }
    
    @Override
    public Long getPlatId() {
        return user().getPlatId();
    }
    
    @Override
    public Long getParentPlat() {
        return user().getParentId();
    }
    
    @Override
    public Integer getSysFlag() {
        return 1;
    }
    
    @Override
    public Integer getUserFlag() {
        return user().getUserFlag().getValue();
    }
    
    @Override
    public Integer[] getRoleFlag() {
        return new Integer[0];
    }

    @Override
    public String getLanguage() {
        return user().getLanguage();
    }

    private TokenMsg user() {
        return ApiThreadLocal.get() == null ? new TokenMsg(0L, 0L, UserFlag.REGISTER, 0L,null) : ApiThreadLocal.get();
    }
}
