package com.project.business.config.red;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.ExtendConfig;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
public class RedRequest extends ExtendConfig {
    
    private static final long serialVersionUID = -9076423243391411652L;
    @JSONField(defaultValue = "三方分公司ID")
    private String companyId;
    @JSONField(defaultValue = "第三方用户ID")
    private String userId;
    @JSONField(defaultValue = "群组或聊天室ID")
    private Long placeId;
    @JSONField(defaultValue = "群组或聊天室名称")
    private String placeName;
    @JSONField(defaultValue = "支付密码 看第三方是否需要")
    private String payPass;
    @JSONField(defaultValue = "操作金额")
    private BigDecimal redAmount;
    @JSONField(defaultValue = "红包ID")
    private Long redId;
    @JSONField(defaultValue = "操作类型 SEND:发红包 、LUCKY:抢红包")
    private String operate;
}
