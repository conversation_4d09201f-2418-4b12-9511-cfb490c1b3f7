package com.project.business.api.controller.login;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.api.util.VerificationCodeImgUtil;
import com.project.business.customer.model.CompanyConfig;
import com.project.business.customer.model.PlatUser;
import com.project.business.customer.model.UserInfo;
import com.project.business.dict.UserFlag;
import com.project.business.manager.model.PlatInfo;
import com.project.business.service.bean.TokenBean;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.config.AppConfigService;
import com.project.business.service.plat.CompanyConfigService;
import com.project.business.service.plat.PlatDomainService;
import com.project.business.service.plat.PlatInfoService;
import com.project.business.service.user.UserService;
import com.project.business.service.user.vo.*;
import com.project.framework.aop.annotation.EscapeWrapper;
import com.project.framework.bean.SmsVo;
import com.project.framework.common.util.IpRemote;
import com.project.framework.common.util.RandomUtil;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import com.project.framework.core.exception.Language;
import com.project.framework.producer.code.BusinessEnum;
import com.project.framework.producer.code.TopicEnum;
import com.project.framework.producer.model.PushPrivateMessageVo;
import com.project.framework.producer.service.MessageService;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redis.RedisKeyPrefix;
import com.project.framework.service.SmsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/apiChat")
public class LoginController {
    
    private static final Logger log = LoggerFactory.getLogger(LoginController.class);

    private final static String key = RedisKeyConstant.USER_LOGIN_ERROR_NUM;
    private final static int USER_LOGIN_ERROR_NUM = 3;

    @Autowired
    private RedisClient redisClient;
    @Autowired
    private UserService userService;
    @Autowired
    private PlatDomainService platDomainService;
    @Autowired
    private PlatInfoService platInfoService;
    @Autowired
    private MessageService redisMessageService;
    @Autowired
    private CompanyConfigService companyConfigService;
    @Value("${spring.profiles.active}")
    private String active;
    @Autowired
    private SmsService smsService;
    @Autowired
    private AppConfigService appConfigService;
    
    @EscapeWrapper
    @RequestMapping(value = "/validateCode")
    public String validateCode(HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 设置响应的类型格式为图片格式
        response.setContentType("image/jpeg");
        // 禁止图像缓存。
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        String id = request.getSession().getId();
        VerificationCodeImgUtil vCode = new VerificationCodeImgUtil(150, 40, 4, 100);
        final String key = RedisKeyConstant.VERIFICATION_CODE + id;
        log.info("sessionId : {} , 验证码: {}", id, vCode.getCode());
        redisClient.set(RedisKeyPrefix.BUSINESS, key, vCode.getCode(), 3000);
        vCode.write(response.getOutputStream());
        return null;
    }
    
    @RequestMapping(value = "/sendSms")
    public Object sendSms(HttpServletRequest request, @RequestBody @Valid SendSmsVo reqVo) {
//        checkVerCode(request, reqVo.getVerCode());
        //TODO: 校验手机号码
        if (userService.checkPhone(reqVo.getPhone()) && reqVo.getSmsType() == SmsType.REGISTER) {
            throw new GlobalException(Cause.error_user_phone_register);
        } else if (!userService.checkPhone(reqVo.getPhone()) && reqVo.getSmsType() == SmsType.FORGOT) {
            throw new GlobalException(Cause.error_user_phone_forgot);
        }
        
        return sendSms(reqVo.getPhone(), reqVo.getSmsType());
    }
    
    private void checkVerCode(HttpServletRequest request, String verCode) {
        if (StringUtils.isBlank(verCode)){
            throw new GlobalException(Cause.blank_user_graph_code);
        }
        String id = request.getSession().getId();
        final String key = RedisKeyConstant.VERIFICATION_CODE + id;
        String code = (String) redisClient.get(RedisKeyPrefix.BUSINESS, key);
        if (StringUtils.isEmpty(code)) {
            throw new GlobalException(Cause.expired_user_graph_code);
        }
        if (!code.equalsIgnoreCase(verCode)) {
            throw new GlobalException(Cause.error_user_graph_code);
        }
    }
    
    private Map<String,String> sendSms(String phoneNum, SmsType smsType) {
        final String key = RedisKeyConstant.SEND_NUM + phoneNum;
        int count = 1;
        if (null != redisClient.get(RedisKeyPrefix.CACHE, key)) {
            count = (Integer) redisClient.get(RedisKeyPrefix.CACHE, key);
            count++;
        }
        if (count > 5) {
            throw new GlobalException(Cause.much_user_code);
        }
        redisClient.set(RedisKeyPrefix.CACHE, key, count, 3600);
        final String verCode = RandomUtil.getRandomNum(6);
        redisClient.set(RedisKeyPrefix.CACHE, phoneNum, verCode, 300);
        redisClient.set(RedisKeyPrefix.CACHE, RedisKeyConstant.VALID_NUM + phoneNum + ":" + verCode, 0, 300);

        final Map<String, String> result = new HashMap<>();
        SmsVo smsVo = new SmsVo();
        smsVo.setSmsCode(verCode);
        smsVo.setPhoneNum(phoneNum);
        smsVo.setSmsType(smsType.getValue1());
        if(active.equals("prod")||active.equals("reprod")) {
            //TODO:异步发送短信接口
            smsService.sendSms(smsVo);
            result.put("smsCode","");
        }else {
            smsService.sendSms(smsVo);
            result.put("smsCode",verCode);
        }
        return result;
    }
    
    private void checkSmsCode(final String phoneNum, String verCode) {
        String redisVerCode;
        if (null != redisClient.get(RedisKeyPrefix.CACHE, phoneNum)) {
            redisVerCode = (String) redisClient.get(RedisKeyPrefix.CACHE, phoneNum);
        } else {
            throw new GlobalException(Cause.expired_user_code);
        }
        int validNum = 1;
        final String validNumKey = RedisKeyConstant.VALID_NUM + phoneNum + ":" + redisVerCode;
        if (null != redisClient.get(RedisKeyPrefix.CACHE, validNumKey)) {
            validNum = (Integer) redisClient.get(RedisKeyPrefix.CACHE, validNumKey);
        }
        if (validNum > 3) {
            redisClient.delete(RedisKeyPrefix.CACHE, phoneNum);
            throw new GlobalException(Cause.invalid_user_code);
        } else {
            redisClient.set(RedisKeyPrefix.CACHE, validNumKey, validNum + 1, 300);
        }
        redisVerCode = (String) redisClient.get(RedisKeyPrefix.CACHE, phoneNum);
        if (null != redisVerCode && redisVerCode.equals(verCode)) {
            log.info("手机号码:" + phoneNum + ",用户输入的验证码:" + verCode + ",系统验证码:" + redisVerCode + ".验证成功");
            redisClient.delete(RedisKeyPrefix.CACHE, phoneNum);
        } else {
            log.info("手机号码:" + phoneNum + ",用户输入的验证码:" + verCode + ",系统验证码:" + redisVerCode + ".验证失败");
            throw new GlobalException(Cause.error_decry_failed);
        }
    }
    
    @RequestMapping(value = "/checkSms")
    public Object checkSms(HttpServletRequest request, HttpServletResponse response,
                           @RequestBody @Valid SmsCheckVo reqVo) {
        checkSmsCode(reqVo.getPhone(), reqVo.getSmsCode());
        final String checkCode = RandomUtil.getRandomString(64);
        final Map<String, String> result = new HashMap<>();
        result.put("smsToken", checkCode);
        final String key = RedisKeyConstant.SMS_TOKEN + checkCode;
        redisClient.set(RedisKeyPrefix.CACHE, key, reqVo.getPhone(), 9000);
        return result;
    }

    @EscapeWrapper
    @RequestMapping(value = "/register")
    public Object register(HttpServletRequest request, @RequestBody @Valid RegisterVo reqVo) {
        //参数校验
        if(StringUtils.isBlank(reqVo.getAccount())){        //手机注册
            if (StringUtils.isBlank(reqVo.getPhone()) || StringUtils.isBlank(reqVo.getSmsToken())){
                throw new GlobalException(Cause.params_fail);
            }else {
                reqVo.setAccount(reqVo.getPhone());
            }
        }

        checkVerCode(request, reqVo.getVerCode());
        if (StringUtils.isBlank(reqVo.getLanguage())){
            reqVo.setLanguage(Language.ZH.getLanguage());
        }
        if (reqVo.getPlatId() == null || reqVo.getPlatId() < 1){
            throw new GlobalException(Cause.error_plat_info);
        }
        PlatInfo platInfo = platInfoService.queryPlatInfoById(reqVo.getPlatId());
        if (platInfo == null){
            throw new GlobalException(Cause.error_platId_notfund);
        }
        ApiThreadLocal.set(new TokenMsg(0L, platInfo.getId(), UserFlag.REGISTER, platInfo.getParentPlat(), reqVo.getLanguage()));

        if (StringUtils.isNotBlank(reqVo.getPhone()) && StringUtils.isNotBlank(reqVo.getSmsToken())){
            final String key = RedisKeyConstant.SMS_TOKEN + reqVo.getSmsToken();
            if (redisClient.get(RedisKeyPrefix.CACHE, key) == null) {
                throw new GlobalException(Cause.invalid_user_code);
            } else {
                final String redisPhone = (String) redisClient.get(RedisKeyPrefix.CACHE, key);
                if(!redisPhone.equals(reqVo.getPhone())){
                    throw new GlobalException(Cause.params_fail);
                }
                reqVo.setPhone(redisPhone);
                redisClient.delete(RedisKeyPrefix.CACHE, key);
            }
        }
        return userService.register(reqVo);
    }

    @EscapeWrapper
    @PostMapping("/webLogin")
    public Object webLogin(HttpServletRequest request, HttpServletResponse response, @RequestBody @Valid LoginVo reqVo) throws Exception {
        if (StringUtils.isBlank(reqVo.getLanguage())){
            reqVo.setLanguage(Language.ZH.getLanguage());
        }
        ApiThreadLocal.set(new TokenMsg(null, null, null, null, reqVo.getLanguage()));

        final String sessionKey = key + request.getSession().getId();
        if (redisClient.exists(RedisKeyPrefix.BUSINESS,sessionKey)
                && (Long)redisClient.get(RedisKeyPrefix.BUSINESS,sessionKey) >= USER_LOGIN_ERROR_NUM){
            checkVerCode(request, reqVo.getVerCode());
        }
        if (reqVo.getPlatId() == null || reqVo.getPlatId() < 1){
            throw new GlobalException(Cause.error_plat_info);
        }
        ApiThreadLocal.set(new TokenMsg(null, reqVo.getPlatId(), null, null, reqVo.getLanguage()));
        PlatInfo platInfo = platInfoService.queryPlatInfoById(reqVo.getPlatId());
        if (platInfo == null){
            throw new GlobalException(Cause.error_platId_notfund);
        }
        ApiThreadLocal.set(new TokenMsg(null, platInfo.getId(), UserFlag.REGISTER, platInfo.getParentPlat(), reqVo.getLanguage()));
        final String ipAddr = IpRemote.getIpAddr(request);
        reqVo.setIp(ipAddr);
        return userService.login(reqVo,request.getSession().getId());
    }

    @PostMapping("/userForget")
    public Object userForget(HttpServletRequest request, @RequestBody @Valid UserForgetVo reqVo) {
        final String key = RedisKeyConstant.SMS_TOKEN + reqVo.getSmsToken();
        if (redisClient.get(RedisKeyPrefix.CACHE, key) == null) {
            throw new GlobalException(Cause.invalid_user_code);
        } else {
            final String redisPhone = (String) redisClient.get(RedisKeyPrefix.CACHE, key);
            if(!redisPhone.equals(reqVo.getPhone())){
                throw new GlobalException(Cause.params_fail);
            }
            reqVo.setPhone(redisPhone);
            redisClient.delete(RedisKeyPrefix.CACHE, key);
        }
        userService.userForget(reqVo);
        return null;
    }
    
    @RequestMapping(value = "/thirdLogin")
    public Object thirdLogin(HttpServletRequest request, HttpServletResponse response,
                             @RequestBody @Valid ThirdLoginVo reqVo) {
        final String key = RedisKeyConstant.THIRD_ID + reqVo.getLoginToken();
        if (!redisClient.exists(RedisKeyPrefix.BUSINESS, key)){
            throw new GlobalException(Cause.error_third_user_token);
        }
        PlatUser platUser = (PlatUser) redisClient.get(RedisKeyPrefix.BUSINESS, key);
        ApiThreadLocal.set(new TokenMsg(null, platUser.getPlatId(), null, null, null));
        TokenBean tokenBean = userService.thirdUserAuth(reqVo.getLoginToken());
        ApiThreadLocal.set(new TokenMsg(null, platUser.getPlatId(), null, null, tokenBean.getLanguage()));
        return tokenBean;
    }

    @RequestMapping(value = "/ownLogin")
    public Object ownLogin(HttpServletRequest request, HttpServletResponse response,
                             @RequestBody @Valid ThirdLoginVo reqVo) {
        final String key = RedisKeyConstant.THIRD_ID + reqVo.getLoginToken();
        if (!redisClient.exists(RedisKeyPrefix.BUSINESS, key)){
            throw new GlobalException(Cause.error_third_user_token);
        }
        UserInfo userInfo = (UserInfo) redisClient.get(RedisKeyPrefix.BUSINESS, key);
        ApiThreadLocal.set(new TokenMsg(null, userInfo.getPlatId(), null, null, reqVo.getLanguage()));
        TokenBean tokenBean = userService.ownUserAuth(reqVo.getLoginToken());
        return tokenBean;
    }
    
    @PostMapping(value = "/shareLogin")
    public Object shareLogin(HttpServletRequest request, HttpServletResponse response,
                             @RequestBody @Valid ThirdLoginVo reqVo) {
        final String key = RedisKeyConstant.THIRD_ID + reqVo.getLoginToken();
        if (!redisClient.exists(RedisKeyPrefix.BUSINESS, key)){
            throw new GlobalException(Cause.error_third_user_token);
        }
        PlatUser platUser = (PlatUser) redisClient.get(RedisKeyPrefix.BUSINESS, key);
        ApiThreadLocal.set(new TokenMsg(null, platUser.getPlatId(), null, null, null));
        return userService.shareLogin(reqVo.getLoginToken());
    }

    @PostMapping("/registerType")
    public Object registerType(HttpServletRequest request, HttpServletResponse response,
                               @RequestBody @Valid RegisterTypeVo reqVo){
            return companyConfigService.queryRegisterType(reqVo.getPlatId());
    }

    @PostMapping("/thirdLoginPage")
    public Object thirdLoginPage(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid RegisterTypeVo reqVo){
            return appConfigService.queryThirdLoginPage(reqVo.getPlatId());
    }

    @PostMapping("/bindAccountPage")
    public Object bindAccountPage(HttpServletRequest request, HttpServletResponse response){
        return appConfigService.queryBindAccountPage();
    }

    @PostMapping("/test")
    public Object test() {
        PushPrivateMessageVo pushPrivateMessageVo = new PushPrivateMessageVo();
        pushPrivateMessageVo.setPlatID("190036");
        pushPrivateMessageVo.setUserID("********,********");
        pushPrivateMessageVo.setData("哈哈哈");
        pushPrivateMessageVo.setBusinessEnum(BusinessEnum.PUSH_PRIVATE_MESSAGE);
        redisMessageService.sendMessage(TopicEnum.TOPIC_IM_MESSAGE, pushPrivateMessageVo);
        return "校验通过";
    }

    @PostMapping("/visitorLogin")
    public Object visitorLogin(HttpServletRequest request, HttpServletResponse response, @RequestBody  VisitorLoginVo reqVo) throws Exception {
        if (StringUtils.isBlank(reqVo.getLanguage())){
            reqVo.setLanguage(Language.ZH.getLanguage());
        }
        if (reqVo.getPlatId() == null || reqVo.getPlatId() < 1){
            throw new GlobalException(Cause.error_plat_info);
        }
        PlatInfo platInfo = platInfoService.queryPlatInfoById(reqVo.getPlatId());
        if (platInfo == null){
            throw new GlobalException(Cause.error_platId_notfund);
        }
        ApiThreadLocal.set(new TokenMsg(0L, platInfo.getId(), UserFlag.THIRD, platInfo.getParentPlat(), reqVo.getLanguage()));
        reqVo.setSessionId(request.getSession().getId());
        reqVo.setUserIp(IpRemote.getIpAddr(request));
        return userService.visitorLogin(reqVo);
    }
}
