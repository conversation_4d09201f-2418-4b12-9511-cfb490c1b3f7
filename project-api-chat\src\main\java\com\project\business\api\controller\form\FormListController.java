package com.project.business.api.controller.form;

import com.project.business.service.form.FormListService;
import com.project.business.service.form.vo.FormUrlVo;
import com.project.business.service.form.vo.TranslateUrl;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiChat")
public class FormListController {
    
    @Autowired
    private FormListService formListService;
    
    @PostMapping("/queryFormUrl")
    public Object queryFormUrl(HttpServletRequest request, HttpServletResponse response,
                               @RequestBody @Valid FormUrlVo reqVo) {
        return formListService.queryFormUrl(reqVo);
    }
    
    @PostMapping("/translateUrl")
    public Object translateUrl(HttpServletRequest request, HttpServletResponse response,
                               @RequestBody @Valid TranslateUrl reqVo) {
        return formListService.translateUrl(reqVo);
    }
    
}
