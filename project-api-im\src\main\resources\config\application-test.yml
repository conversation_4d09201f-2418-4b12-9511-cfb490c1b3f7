spring:
  application:
    name: apiIm
  redis:
    data-base: 2
    host: *************
    port: 6679
    password: <EMAIL>
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ***************************************************************************************************************************************************
    username: chat
    password: Abc123!@#.
    driver-class-name: com.mysql.cj.jdbc.Driver
    minimum-idle: 5
    maximum-pool-size: 200
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 100000
    connection-timeout: 10000
    connection-test-query: SELECT 1
    type: com.zaxxer.hikari.HikariDataSource
server:
  port: 9200
redisson:
  address: redis://*************:6679
  data-base: 2
  password: <EMAIL>
  timeout: 30000
security:
  auth-url: /**
  ignore-url: /components/**, /css/**, /js/**, /image/**
sts:
  endpoint: sts.ap-southeast-1.aliyuncs.com
  accessKeyId: LTAI5tKuWG8pcRyXTsfEbBiq
  accessKeySecret: ******************************
  roleArn: acs:ram::5719026860824638:role/chat-img
  roleSessionName: chat-ceshi
  policy: config/policy.json
  path: /securityChat

im:
  url: http://*************/
  apiBalanceUrl: http://*************:9600/apiBalance/