package com.project.business.config.reward;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;


@Setter
@Getter
public class RewardRequest implements Serializable {


    private static final long serialVersionUID = -4193084769823373759L;

    @JSONField(defaultValue = "三方分公司ID")
    private String companyId;
    @JSONField(defaultValue = "第三方用户ID")
    private String userId;
    @JSONField(defaultValue = "群组或聊天室ID")
    private Long placeId;
    @JSONField(defaultValue = "群组或聊天室名称")
    private String placeName;
    @JSONField(defaultValue = "支付密码 看第三方是否需要")
    private String payPass;
    @JSONField(defaultValue = "操作金额")
    private BigDecimal rewardAmount;
    @JSONField(defaultValue = "被打赏人用户ID")
    private Long receiverUserId;


}
