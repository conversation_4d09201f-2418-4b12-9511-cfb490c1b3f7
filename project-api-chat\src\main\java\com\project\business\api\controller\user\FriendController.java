package com.project.business.api.controller.user;

import com.project.business.service.plat.PlatConfigService;
import com.project.business.service.user.UserExtendService;
import com.project.business.service.user.UserFriendService;
import com.project.business.service.user.vo.FriendBaseVo;
import com.project.business.service.user.vo.FriendRemarkVo;
import com.project.framework.core.code.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiChat")
public class FriendController {
    
    @Autowired
    private UserExtendService extendService;
    @Autowired
    private UserFriendService friendService;
    @Autowired
    private PlatConfigService platConfigService;
    
    @PostMapping("/queryAllFriend")
    public Object queryUserFriend(HttpServletRequest request, HttpServletResponse response) {
        return new Result(extendService.queryAllFriend());
    }
    
    @PostMapping("/editFriendRemark")
    public Object editFriendRemark(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid FriendRemarkVo reqVo) {
        friendService.editFriendRemark(reqVo);
        return null;
    }
    
    @PostMapping("/addFriend")
    public Object addFriend(HttpServletRequest request, HttpServletResponse response,
                            @RequestBody @Valid FriendBaseVo reqVo) {
        extendService.addFriend(reqVo.getFriendId());
        return null;
    }
    
    @PostMapping("/platFriendUrl")
    public Object getPlatFriendUrl(HttpServletRequest request, HttpServletResponse response) {
        return platConfigService.queryFriendUrl();
    }
    
}
