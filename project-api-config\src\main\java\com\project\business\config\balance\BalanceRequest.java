package com.project.business.config.balance;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Setter
@Getter
public class BalanceRequest implements Serializable {
    
    private static final long serialVersionUID = -2780878098757443226L;
    @JSONField(defaultValue = "三方分公司ID")
    private String companyId;
    @JSONField(defaultValue = "第三方用户ID")
    private String userId;
}
