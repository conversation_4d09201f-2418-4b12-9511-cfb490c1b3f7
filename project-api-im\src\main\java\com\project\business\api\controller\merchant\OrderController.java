package com.project.business.api.controller.merchant;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.service.bean.MerchantTokenMsg;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.merchant.MerchantOrderService;
import com.project.business.service.merchant.vo.MerchantOrderEventVo;
import com.project.business.service.merchant.vo.MerchantOrderVo;
import com.project.business.service.merchant.vo.MerchantTransferSessionVo;
import com.project.business.service.token.TokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 银商IM接口
 */
@RestController
@RequestMapping("/apiIm")
public class OrderController {
    
    @Autowired
    private MerchantOrderService orderService;
    @Autowired
    private TokenService tokenService;
    
    @ResponseBody
    @PostMapping("/getMerchantOrder")
    public Object getMerchantOrder(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid MerchantOrderVo reqVo) throws Exception {
        MerchantTokenMsg tokenMsg1 = tokenService.checkMerchantToken(reqVo.getUserToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,tokenMsg1.getLanguage()));
        return orderService.getMerchantOrder(reqVo);
    }
    
    @ResponseBody
    @PostMapping("/merchantOrderEvent")
    public Object merchantOrderEvent(HttpServletRequest request, HttpServletResponse response,
                                     @RequestBody @Valid MerchantOrderEventVo reqVo) throws Exception {
        MerchantTokenMsg tokenMsg1 = tokenService.checkMerchantToken(reqVo.getUserToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,tokenMsg1.getLanguage()));
        return orderService.merchantOrderEvent(reqVo);
    }

    @ResponseBody
    @PostMapping("/merchantTransferSession")
    public Object transferSession(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody @Valid MerchantTransferSessionVo reqVo) throws Exception {
        MerchantTokenMsg merchantToken = tokenService.checkMerchantToken(reqVo.getMerchantToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,merchantToken.getLanguage()));
        orderService.transferSession(reqVo);
        return null;
    }
}
