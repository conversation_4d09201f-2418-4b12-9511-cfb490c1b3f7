package com.project.business.config.merchant;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PayAccountMessageBodyVo implements Serializable {
    
    private static final long serialVersionUID = 6942254466615397104L;
    private String MsgType = "PAY_ACCOUNT";
    private String OrderId;
    private BigDecimal Amount;
    private String MsgContent = "您好，欢迎来到官方充值系统，微信充值如下。请进行充值，充值完毕，请点击[完成充值]！";
    private PayAccountMsgDetail MsgDetail;
}
