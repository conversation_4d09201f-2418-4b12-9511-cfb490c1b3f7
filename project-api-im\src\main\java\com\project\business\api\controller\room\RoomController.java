package com.project.business.api.controller.room;

import com.project.business.service.room.RoomInfoService;
import com.project.business.service.room.vo.RoomBaseVo;
import com.project.framework.core.code.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiIm")
public class RoomController {
    
    @Autowired
    private RoomInfoService roomService;
    
    @PostMapping("/queryRoomInfo")
    public Object queryByRoomId(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid RoomBaseVo reqVo) {
        return roomService.queryOne(reqVo.getRoomId());
    }
    
    @PostMapping("/queryRooms")
    public Object queryRoomsByPlat(HttpServletRequest request, HttpServletResponse response) {
        return new Result(roomService.queryRoomsByPlat());
    }
    
}
