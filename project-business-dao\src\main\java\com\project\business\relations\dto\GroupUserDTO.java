package com.project.business.relations.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class GroupUserDTO implements Serializable {
    private static final long serialVersionUID = 776805886309460936L;
    @JSONField(name = "UserID")
    private Long userId;
    private String iconUrl;
    private String groupNickName;
    @JSONField(name = "RoleID")
    private Long roleId;
    private String managerName;
}
