package com.project.business.api.controller.live;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.dict.UserType;
import com.project.business.service.bean.MerchantTokenMsg;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.live.LiveOrderService;
import com.project.business.service.live.vo.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.project.business.service.token.TokenService;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redis.RedisKeyPrefix;
import com.project.framework.redisson.RedissLockUtil;
import com.project.framework.redisson.RedissonLockerClient;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 客服工单接口
 *
 * <AUTHOR>
 * @email <EMAIL>
 */
@RestController
@RequestMapping("/apiIm")
public class LiveOrderController {

    @Autowired
    private LiveOrderService orderService;
    @Autowired
    private TokenService tokenService;
    
    @ResponseBody
    @PostMapping("/createLiveSession")
    public Object getMerchantOrder(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid LiveOrderVo reqVo) throws Exception {
        MerchantTokenMsg tokenMsg1 = tokenService.checkMerchantToken(reqVo.getUserToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,tokenMsg1.getLanguage()));
        RLock lock = RedissLockUtil.lock(RedisKeyPrefix.LOCK + RedisKeyConstant.LIVE_ORDER, 30);
        try {
            return orderService.createLiveSession(reqVo);
        }finally {
            if (lock != null && lock.isLocked()) {
                lock.unlock();
            }
        }
    }
    
    @ResponseBody
    @PostMapping("/liveOrderEvent")
    public Object getMerchantOrder(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid LiveOrderEventVo reqVo) throws Exception {
        MerchantTokenMsg tokenMsg1 = tokenService.checkMerchantToken(reqVo.getUserToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,tokenMsg1.getLanguage()));
        return orderService.liveOrderEvent(reqVo);
    }
    
    @PostMapping("/liveSessionHistory")
    public Object sessionHistory(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid SessionHistoryVo reqVo) throws Exception {
        MerchantTokenMsg tokenMsg1 = tokenService.checkMerchantToken(reqVo.getUserToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,tokenMsg1.getLanguage()));
        orderService.saveSessionHistory(reqVo);
        return null;
    }

    @PostMapping("/liveLastSpeakTime")
    public Object sessionLastSpeakTime(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid SessionLastSpeakVo reqVo) throws Exception {
        MerchantTokenMsg tokenMsg1 = tokenService.checkMerchantToken(reqVo.getLiveToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,tokenMsg1.getLanguage()));
        orderService.sessionLastSpeakTime(reqVo);
        return null;
    }

    @PostMapping("/liveTransferSession")
    public Object transferSession(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody @Valid TransferSessionVo reqVo) throws Exception {
        MerchantTokenMsg liveToken = tokenService.checkMerchantToken(reqVo.getLiveToken());
        ApiThreadLocal.set(new TokenMsg(0L,0L,null,0L,liveToken.getLanguage()));
        orderService.transferSession(reqVo);
        return null;
    }
    
}
