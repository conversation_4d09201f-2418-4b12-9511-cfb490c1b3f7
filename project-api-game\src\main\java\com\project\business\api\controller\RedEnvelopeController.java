package com.project.business.api.controller;

import com.project.business.service.game.EventConfigService;
import com.project.business.service.game.RedEnvelopeService;
import com.project.business.service.game.vo.*;
import com.project.framework.common.service.ISessionService;
import com.project.framework.redisson.RedissLockUtil;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 红包控制器
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-01-05 19:14:14
 */
@RestController
@RequestMapping("/apiGame")
public class RedEnvelopeController {

    @Autowired
    private RedEnvelopeService envelopeService;
    @Autowired
    private EventConfigService eventConfigService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private RedConfig displayValues;

    @PostMapping("/queryRedEnvelopeGotTimes")
    public Object queryRedEnvelopeGotTimes(HttpServletRequest request, HttpServletResponse response) {
        return envelopeService.queryRedEnvelopeGotTimes();
    }

    @PostMapping("/redEnvelopeTopFlag")
    public Object redEnvelopeTopFlag(HttpServletRequest request, HttpServletResponse response,
                                     @RequestBody @Valid UserRedEnvelopeMangerVo reqVo) {
        return envelopeService.redEnvelopeTopFlag(reqVo);
    }

    @PostMapping("/queryRedEnvelopeTop")
    public Object queryRedEnvelopeTop(HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody @Valid RedEnvelopeTop reqVo) {
        return envelopeService.queryRedEnvelopeTop(reqVo);
    }

    @PostMapping("/sendRedEnvelope")
    public Object sendRedEnvelope(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody @Valid RedEnvelopeVo reqVo) {
        return envelopeService.sendRedEnvelope(reqVo);
    }

    @PostMapping("/queryRedEnvelope")
    public Object queryRedEnvelope(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid RedEnvelopeBaseVo reqVo) {
        return envelopeService.queryRedEnvelope(reqVo);
    }

    @PostMapping("/luckyRedEnvelope")
    public Object luckyRedEnvelope(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid RedEnvelopeBaseVo reqVo) {
        final String key = "LUCKY_RED_ENVELOPE";
        final Long userId = sessionService.getUserId();
        RLock rlock = RedissLockUtil.multiLock(300, key + reqVo.getRedId(), key + userId);
        try {
            return envelopeService.luckyRedEnvelope(reqVo, displayValues.getDisplayValues());
        } finally {
            rlock.unlock();
        }
    }

    @PostMapping("/redDetail")
    public Object redDetail(HttpServletRequest request, HttpServletResponse response,
                            @RequestBody @Valid RedEnvelopeBaseVo reqVo) {
        return envelopeService.redDetail(reqVo);
    }

    @PostMapping("/redSuccess")
    public Object success(HttpServletRequest request, HttpServletResponse response) {
        return true;
    }

    @PostMapping("/querySelfRedEnvelopeTotal")
    public Object querySelfRedEnvelopeTotal(HttpServletRequest request, HttpServletResponse response,
                                            @RequestBody SelfRedEnvelopeVo reqVo) {
        return envelopeService.querySelfRedEnvelopeTotal(reqVo);
    }

    @PostMapping("/querySend")
    public Object querySendRedEnvelope(HttpServletRequest request, HttpServletResponse response,
                                       @RequestBody SelfRedEnvelopeVo reqVo) {
        return envelopeService.querySendRedEnvelope(reqVo);
    }

    @PostMapping("/queryReceive")
    public Object queryReceiveRedEnvelope(HttpServletRequest request, HttpServletResponse response,
                                          @RequestBody SelfRedEnvelopeVo reqVo) {
        return envelopeService.queryReceiveRedEnvelope(reqVo);
    }

    @PostMapping("/queryEvent")
    public Object queryEvent(HttpServletRequest request, HttpServletResponse response
    ) {

        return eventConfigService.queryEvent();
    }

    @PostMapping("/getPrize")
    public Object getPrize(HttpServletRequest request, HttpServletResponse response, @RequestBody PrizevVo prizevVo
    ) {
        final String key = "LUCKY_PRIZE";
        RLock rlock = RedissLockUtil.multiLock(300, key + prizevVo.getEventId());
        try {
            return eventConfigService.getPrize(prizevVo);
        } finally {
            rlock.unlock();
        }


    }

    @PostMapping("/queryPrizeLog")
    public Object queryPrizeLog(HttpServletRequest request, HttpServletResponse response, @RequestBody PrizevVo prizevVo
    ) {
        return eventConfigService.queryPrizeLog(prizevVo);
    }

    @PostMapping("/enterRoom")
    public Object enterRoom(HttpServletRequest request, HttpServletResponse response, @RequestBody RoomRecordDTO prizevVo) {

//        String key = RedisKeyConstant.CHATROOM_ONLINE_STATS + prizevVo.getPlatId() + ":" + prizevVo.getPlaceId();
        String key = "CHATROOM_ONLINE_STATS" + prizevVo.getPlatId() + prizevVo.getPlaceId();
        RLock rlock = RedissLockUtil.multiLock(10, key);
        try {
            return eventConfigService.enterRoom(prizevVo);
        } finally {
            rlock.unlock();
        }

    }

    @PostMapping("/exitRoom")
    public Object exitRoom(HttpServletRequest request, HttpServletResponse response, @RequestBody RoomRecordDTO prizevVo
    ) {
//        String key = RedisKeyConstant.CHATROOM_ONLINE_STATS + prizevVo.getPlatId() + ":" + prizevVo.getPlaceId();
        String key = "CHATROOM_ONLINE_STATS" + prizevVo.getPlatId() + prizevVo.getPlaceId();
        RLock rlock = RedissLockUtil.multiLock(10, key);
        try {
            return eventConfigService.exitRoom(prizevVo);
        } finally {
            rlock.unlock();
        }
    }

    @PostMapping("/roomMemberCount")
    public Object roomMemberCount(HttpServletRequest request, HttpServletResponse response, @RequestBody RoomMemberDTO prizevVo
    ) {
        return eventConfigService.roomMemberCount(prizevVo);
    }
}
