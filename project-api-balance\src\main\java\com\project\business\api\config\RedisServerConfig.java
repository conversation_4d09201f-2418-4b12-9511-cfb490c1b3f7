package com.project.business.api.config;

import com.project.framework.redis.RedisConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

@Configuration
@AutoConfigureAfter(RedisConfig.class)
public class RedisServerConfig {
    
    private final static Logger logger = LoggerFactory.getLogger(RedisServerConfig.class);
    
    @Bean
    public RedisMessageListenerContainer redisContainer(LettuceConnectionFactory factory) {
        logger.info("REDIS通道加载");
        final RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(factory);
        return container;
    }
    
}
