package com.project.business.config.merchant;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class PrePayMessageBodyVo implements Serializable {
    
    private static final long serialVersionUID = -6117314569169644819L;
    private String MsgType = "PRE_PAY";
    private String OrderId;
    private BigDecimal Amount;
    private String MsgContent = "订单号：${OrderId}，金额：${Amount}。请选付款方式，专人为您提供相关付款信息。";
    private String EventKey = "GET_PAY_CHANNEL";
    private PrePayEvent[] EventList;
}
