package com.project.business.api;

import com.alibaba.fastjson.serializer.PascalNameFilter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.project.business.api.filter.ApiInterceptor;
import com.project.business.api.filter.TokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

import java.util.ArrayList;
import java.util.List;

@Configuration
public class ProjectConfigurer extends WebMvcConfigurationSupport {
    
    @Autowired
    private TokenInterceptor tokenInterceptor;
    @Autowired
    private ApiInterceptor apiInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 通过registry来注册拦截器，通过addPathPatterns来添加拦截路径
        registry.addInterceptor(this.apiInterceptor).addPathPatterns("/apiChat/**");
        registry.addInterceptor(this.tokenInterceptor).addPathPatterns("/apiChat/**")
                .excludePathPatterns("/apiChat/validateCode", "/apiChat/sendSms", "/apiChat/checkSms","/apiChat/userForget",
                        "/apiChat/register", "/apiChat/registerType", "/apiChat/webLogin", "/apiChat/thirdLogin", "/apiChat/topUpLogin",
                        "/apiChat/liveLogin", "/apiChat/serverLogin", "/apiChat/*Login", "/apiChat/translateText", "/apiChat/apiTest",
                        "/apiChat/thirdLoginPage");
        super.addInterceptors(registry);
    }
    
    @Override
    protected void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        super.configureMessageConverters(converters);
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter();
        converters.add(stringConverter);
        // 1、定义一个convert转换消息的对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        // 2、添加fastjson的配置信息
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        PascalNameFilter upperFilter = new PascalNameFilter();
        SimplePropertyPreFilter filter = new SimplePropertyPreFilter();
        String[] fields = new String[]{"id", "enableFlag", "delFlag", "createId", "updateId", "createTime",
                "updateTime", "platId", "dataCode"};
        for (String field : fields) {
            filter.getExcludes().add(field);
        }
        
        fastJsonConfig.setSerializerFeatures(SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteNullStringAsEmpty, SerializerFeature.WriteNullNumberAsZero,
                SerializerFeature.WriteEnumUsingToString);
        fastJsonConfig.setSerializeFilters(filter, upperFilter);
        fastJsonConfig.setDateFormat("yyyy-MM-dd HH:mm:ss");
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        supportedMediaTypes.add(MediaType.APPLICATION_ATOM_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_FORM_URLENCODED);
        supportedMediaTypes.add(MediaType.APPLICATION_OCTET_STREAM);
        supportedMediaTypes.add(MediaType.APPLICATION_PDF);
        supportedMediaTypes.add(MediaType.APPLICATION_RSS_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XHTML_XML);
        supportedMediaTypes.add(MediaType.APPLICATION_XML);
        supportedMediaTypes.add(MediaType.IMAGE_GIF);
        supportedMediaTypes.add(MediaType.IMAGE_JPEG);
        supportedMediaTypes.add(MediaType.IMAGE_PNG);
        supportedMediaTypes.add(MediaType.TEXT_EVENT_STREAM);
        supportedMediaTypes.add(MediaType.TEXT_HTML);
        supportedMediaTypes.add(MediaType.TEXT_MARKDOWN);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        supportedMediaTypes.add(MediaType.TEXT_XML);
        supportedMediaTypes.add(MediaType.MULTIPART_FORM_DATA);
        fastConverter.setSupportedMediaTypes(supportedMediaTypes);
        // 3、在convert中添加配置信息
        fastConverter.setFastJsonConfig(fastJsonConfig);
        // 4、将convert添加到converters中
        converters.add(fastConverter);
    }
    
}
