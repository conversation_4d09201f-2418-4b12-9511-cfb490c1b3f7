<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>project-framework-parent</artifactId>
    <groupId>com.project.framework</groupId>
    <version>1.0.0</version>
    <relativePath>../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>project-api-chat</artifactId>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.project.framework</groupId>
      <artifactId>project-business-dao</artifactId>
      <version>1.0.0</version>
    </dependency>

    <dependency>
      <groupId>com.project.framework</groupId>
      <artifactId>project-framework-aop</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.project.framework</groupId>
      <artifactId>project-framework-producer</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.project.framework</groupId>
      <artifactId>project-business-api</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>


  <build>
    <finalName>apiChat</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <fork>true</fork>
        </configuration>
      </plugin>
    </plugins>
    <resources>
      <resource>
        <directory>${basedir}/src/main/webapp</directory>
        <!--注意此次必须要放在此目录下才能被访问到 -->
        <targetPath>META-INF/resources</targetPath>
        <includes>
          <include>**/**</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <includes>
          <include>**/**</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <targetPath>BOOT-INF/classes/</targetPath>
        <includes>
          <include>**/**</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <filtering>true</filtering>
        <includes>
          <include>**/*.yml</include>
          <include>**/*.json</include>
        </includes>
      </resource>
      <resource>
        <directory>${basedir}/src/main/resources</directory>
        <excludes>
          <exclude>**/*.yml</exclude>
        </excludes>
      </resource>
    </resources>
  </build>
</project>