package com.project.business.config.merchant;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
public class PayAccountMsgDetail implements Serializable {
    
    private static final long serialVersionUID = 4179489643842533468L;
    private String MsgImg;
    private MsgList[] msgList;

    private List<MsgList[]> msgAllList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MsgList implements Serializable {
        
        private static final long serialVersionUID = -4566744997427446256L;
        private String MsgKey;
        private String MsgValue;
    }

}
