<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true">
  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

  <!-- The FILE and ASYNC appenders are here as examples for a production configuration -->
  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>../logs/apiGame.%d{yyyy-MM-dd}.log</fileNamePattern>
      <maxHistory>90</maxHistory>
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
    </rollingPolicy>
    <encoder>
      <charset>UTF-8</charset>
      <Pattern>%d %-5level [%thread] %logger{0}: %msg%n</Pattern>
    </encoder>
  </appender>

  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
    <queueSize>512</queueSize>
    <appender-ref ref="FILE"/>
  </appender>


  <logger name="com.project.*" level="INFO"/>
  <logger name="com.project.business.**.dao" level="DEBUG"/>
  <logger name="io.netty" level="WARN"/>
  <logger name="javax.management.remote.rmi" level="WARN"/>
  <logger name="io.lettuce" level="WARN"/>
  <logger name="javax.activation" level="WARN"/>
  <logger name="org.redisson" level="WARN"/>
  <logger name="com.atomikos.icatch" level="WARN"/>
  <logger name="com.alibaba" level="WARN"/>
  <logger name="javax.mail" level="WARN"/>
  <logger name="javax.xml.bind" level="WARN"/>
  <logger name="ch.qos.logback" level="WARN"/>
  <logger name="com.codahale.metrics" level="WARN"/>
  <logger name="com.netflix" level="WARN"/>
  <logger name="com.netflix.discovery" level="INFO"/>
  <logger name="com.ryantenney" level="WARN"/>
  <logger name="com.jcraft" level="WARN"/>
  <logger name="com.sun" level="WARN"/>
  <logger name="com.zaxxer" level="WARN"/>
  <logger name="net.sf.ehcache" level="WARN"/>
  <logger name="org.apache" level="WARN"/>
  <logger name="org.bson" level="WARN"/>
  <logger name="org.hibernate.validator" level="WARN"/>
  <logger name="org.hibernate" level="WARN"/>
  <logger name="org.springframework" level="WARN"/>
  <logger name="org.springframework.web" level="WARN"/>
  <logger name="org.springframework.security" level="WARN"/>
  <logger name="org.springframework.cache" level="WARN"/>
  <logger name="org.thymeleaf" level="WARN"/>
  <logger name="org.xnio" level="WARN"/>
  <logger name="springfox" level="WARN"/>
  <logger name="sun.rmi" level="WARN"/>
  <logger name="liquibase" level="WARN"/>
  <logger name="sun.rmi.transport" level="WARN"/>
  <logger name="org.mybatis.spring" level="WARN"/>
  <logger name="com.alibaba.druid" level="WARN"/>
  <logger name="com.zaxxer.hikari" level="DEBUG"/>
  <logger name="sun.net.www" level="WARN"/>
  <logger name="org.jboss.logging" level="WARN"/>
  <logger name="org.springframework.jndi.*" level="WARN"/>
  <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
    <resetJUL>true</resetJUL>
  </contextListener>

  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <charset>UTF-8</charset>
      <pattern>${CONSOLE_LOG_PATTERN}</pattern>
    </encoder>
  </appender>

  <root level="DEBUG"> <!--<root level="@logback.loglevel@">-->
    <appender-ref ref="FILE"/>
    <appender-ref ref="CONSOLE"/>
  </root>

</configuration>
