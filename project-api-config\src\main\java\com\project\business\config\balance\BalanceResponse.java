package com.project.business.config.balance;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class BalanceResponse implements Serializable {
    
    private static final long serialVersionUID = -7019948611306190259L;
    @JSONField(defaultValue = "200成功 非200错误", ordinal = 1)
    private Long code;
    @JSONField(defaultValue = "错误或者成功信息", ordinal = 2)
    private String message;
    
    @JSONField(defaultValue = "{ 'balance':'会员余额', 'dayRecharge':'日充值金额', 'dayBetting':'日投注金额' }", ordinal = 3)
    private BalanceInfo balanceInfo;
}
