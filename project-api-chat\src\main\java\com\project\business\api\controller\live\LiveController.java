package com.project.business.api.controller.live;

import com.project.business.api.util.ApiThreadLocal;
import com.project.business.customer.model.PlatUser;
import com.project.business.dict.UserFlag;
import com.project.business.manager.model.PlatConfig;
import com.project.business.service.bean.TokenMsg;
import com.project.business.service.live.LiveService;
import com.project.business.service.plat.PlatConfigService;
import com.project.business.service.plat.PlatInfoService;
import com.project.business.service.user.vo.ServerLoginVo;
import com.project.business.service.user.vo.ThirdLoginVo;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.project.framework.core.exception.Language;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redis.RedisKeyPrefix;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.support.RequestContextUtils;

@RestController
@RequestMapping("/apiChat")
public class LiveController {
    
    @Autowired
    private LiveService liveService;
    @Autowired
    private PlatConfigService platConfigService;
    @Autowired
    private PlatInfoService platInfoService;
    @Autowired
    private RedisClient redisClient;
    
    @PostMapping("/liveLogin")
    public Object liveLogin(HttpServletRequest request, @RequestBody @Valid ThirdLoginVo reqVo) {
        final String key = RedisKeyConstant.PLAT_ID + reqVo.getLoginToken();
        if (!redisClient.exists(RedisKeyPrefix.BUSINESS, key)){
            throw new GlobalException(Cause.error_third_user_token);
        }
        Long platId = (Long) redisClient.get(RedisKeyPrefix.BUSINESS, key);
        ApiThreadLocal.set(new TokenMsg(null, platId, null, null, null));
        return liveService.liveLogin(reqVo, request);
    }
    
    @PostMapping("/serverLogin")
    public Object liveLogins(HttpServletRequest request, @RequestBody @Valid ServerLoginVo reqVo) {
        ApiThreadLocal.set(new TokenMsg(0L, 0L, UserFlag.THIRD, 0L,
                StringUtils.isBlank(reqVo.getLanguage()) ? Language.unResolve(RequestContextUtils.getLocale(request).getLanguage()) : reqVo.getLanguage()
        ));
        final PlatConfig platConfig = platConfigService.queryPlatConfigByAppId(reqVo.getServerNo());
        if (platConfig == null) {
            throw new GlobalException(Cause.error_platId_access);
        }
        final Long parentPlat = platConfig.getPlatId();
        ApiThreadLocal.setParentId(parentPlat);
        final Long platId = platInfoService.queryPlatIdByCompanyId(reqVo.getCompanyId());
        ApiThreadLocal.setPlatId(platId);
        return liveService.serverLogin(reqVo, request);
    }

    @PostMapping("/liveQuestionGroup")
    public Object liveQuestionGroup(HttpServletRequest request) {
        return liveService.queryLiveQuestionGroup();
    }

    @PostMapping("/liveBanner")
    public Object liveBanner(HttpServletRequest request) {
        return liveService.queryLiveBanner();
    }

    @PostMapping("/LiveMarquee")
    public Object liveMarquee(HttpServletRequest request) {
        return liveService.queryLiveMarquee();
    }

}
