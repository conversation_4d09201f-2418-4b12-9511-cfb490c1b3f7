spring:
  application:
    name: api<PERSON><PERSON>
  redis:
    data-base: 2
    host: ********
    port: 63795
    password: jhd83sdfde#ssS
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ************************************************************************************************************************************************************************************************************************ # 数据库地址
    username: user_java # 数据库用户名
    password: A8d#4kL!2mQ # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver # 数据库驱动
    type: com.zaxxer.hikari.HikariDataSource
    minimum-idle: 5
    maximum-pool-size: 500
    auto-commit: true
    idle-timeout: 30000
    max-lifetime: 100000
    connection-timeout: 40000
    connection-test-query: SELECT 1
    validation-timeout: 30000
    time-between-eviction-runs-millis: 300000
server:
  port: 9100
redisson: # 分布式事物锁redis配置
  address: redis://********:63795
  data-base: 2
  password: jhd83sdfde#ssS
  timeout: 30000
security:
  auth-url: /**
  ignore-url : /components/**, /css/**, /js/**, /image/**

sts:
  endpoint: sts.ap-southeast-1.aliyuncs.com
  accessKeyId: LTAI5tKuWG8pcRyXTsfEbBiq
  accessKeySecret: ******************************
  roleArn: acs:ram::5719026860824638:role/chat-img
  roleSessionName: chat-ceshi
  policy: config/policy.json
  path: /securityChat

im:
  url: http://*********:8090/
  apiBalanceUrl: http://*********/apiBalance/

tencent:
  translate:
    secret-id: AKIDhw2d0BBHV5VkFVvWCG3buwilmYhe9ZXE
    secret-key: bEpb9Rt7BD46LMKCpBLJ0RedkbBUERO4
    endpoint: tmt.tencentcloudapi.com
    region: ap-hongkong
  sms:
    secret-id: AKIDzoOjoc9PO7ZlhNmtjIbeRxB8oDLCn8HA
    secret-key: cx1pX5OTIrAE2cw0pOhkY6OPAk49zZu1

third:
  company:
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMPe7cMx/RwrX4IgtVtOybwUNfZmrPBzhIuK3RqhMWKUlfgDGvJZnqu1PxtZL+VkgwaJ6kqg2n4JWsxR+T2GFBAGgn15zIPz6Cnq7GQsIbHQ5P9bF+eBckkn6fbVSa4vLQXXqqzKd7sdBc+Q4LjcEKFkBf0YjUE+n0A6KlZCGyunAgMBAAECgYBwPag76cJkSQqC9CFYwG5j46UEnNPPOjMjD6EiKGq3VfQWWhkv9WEyfjs6fcIvjohyz8c9mq15+fxZB0RvGA0YHSQmFoHct65lq3B/JMitXwFRMgS018dXdC/vEE35gk9X5PefQR19Zez5FAuwbI09joSPhsEjqeBLRrCO8i9o0QJBAOQVnr3XW8BaHK03cKlUN9B338BHx2XFzo01CxusSLLci0DPsdt2V/vNmp90rZZmHykwndrkAp55IKTs0Jlbg8kCQQDb1/xSJk99LJPpilDXxg1aWpgpfI3Tbe8052kAC108SVHpf0YoR+N24BL2OrjkG7EjEB4nNZOYYpDUEhh4tYvvAkALJpNwLIsryDwGq+MyJA/mfTXPLLMu4pDKjoFQhQe41tdn/79uP0bl5JNeoFGsPKnhVaMiBGusW7eg2IMKPFMhAkBcHHPg66JUfRAAQu66aYYAvIKSp1g5JQ+bp/XLifdnogTLA4Oervg0P5BSUKpVEPE4ErG/leggafPqlyZftRlLAkEAmfWBYoJvq7FMXP+fGqc1hxcSOoeYJ4NEODRlCLf3cpDOUz1ArxbZzIN6c6/4L37+Lp+j2tNlVPXzyhxNtP1/4g==
