package com.project.business.quartz.model;

import com.project.framework.database.base.BaseModel;
import lombok.Getter;
import lombok.Setter;
/**
 * 
 * <br>定时任务配置表
 * <b>功能：</b>JobDetailEntity<br>
 * <AUTHOR>
 * @version 1.0
 * @date ${date}
 */
@Getter
@Setter
public class JobDetails extends BaseModel {


	private static final long serialVersionUID = -4579042727449393878L;
	//
	private String jobName;
	//
	private String jobGroup;
	//
	private String jobCron;

	private String jobClass;
	//
	private String jobParams;
	//
	private String description;
	//
	private String vmParam;
	//
	private String jarPath;
	//
	private String status;
}

