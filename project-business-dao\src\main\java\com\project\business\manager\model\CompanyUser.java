package com.project.business.manager.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.dict.SysFlag;
import com.project.framework.database.base.BaseModel;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 
 * <br>后台登陆用户信息
 * <b>功能：</b>SysUserEntity<br>
 * <AUTHOR>
 * @version 1.0
 * @date ${date}
 */
@Getter
@Setter
public class CompanyUser extends BaseModel {
	private static final long serialVersionUID = 3020804129113464156L;
	//是否绑定谷歌验证 0 未绑定 ,1 已绑定
	private Integer bindGoogle;
	//谷歌验证码
	@JSONField(serialize=false)
	private String googleSecret;
	//登陆名称
	private String loginName;
	//昵称
	private String nickName;
	//登陆密码
	@JSONField(serialize=false)
	private String password;
	//重置标记 0 无需重置 ,1 需要重置 
	private Integer resetFlag;

	private Long roleId;

	private String roleName;
	private String roleNameEn;

	private Long roleType;
	//系统标记 1:系统 2:平台
	private SysFlag sysFlag;
	//分公司id
	private Long companyId;

	private List<Map<String,Object>> platList;

}

