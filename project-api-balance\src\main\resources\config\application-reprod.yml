spring:
  application:
    name: apiBalance
  redis:
    data-base: 3
    host: redis-java
    port: 6379
    password: Jsl#dsu973yhd
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ******************************************************************************************************************************************* # 数据库地址
    username: dev_user # 数据库用户名
    password: dev_password # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver # 数据库驱动
    type: com.zaxxer.hikari.HikariDataSource
    minimum-idle: 5
    maximum-pool-size: 200
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 100000
    connection-timeout: 10000
    connection-test-query: SELECT 1
    time-between-eviction-runs-millis: 300000
server:
  port: 9600
redisson: # 分布式事物锁redis配置
  address: redis://redis-java:6379
  data-base: 2
  password: Jsl#dsu973yhd
  timeout: 30000
im:
  url: http://go-api-chat:8090/
