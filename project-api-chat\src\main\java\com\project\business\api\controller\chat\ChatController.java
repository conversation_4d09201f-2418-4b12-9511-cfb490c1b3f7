package com.project.business.api.controller.chat;

import com.project.business.customer.model.CompanyConfig;
import com.project.business.service.button.LevitateButtonService;
import com.project.business.service.common.ChatQuickBackService;
import com.project.business.service.plat.CompanyConfigService;
import com.project.framework.core.code.Result;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiChat")
public class ChatController {

    @Resource
    private ChatQuickBackService chatQuickBackService;

    @Resource
    private LevitateButtonService levitateButtonService;

    @Resource
    private CompanyConfigService companyConfigService;


    @PostMapping("/quickBack")
    public Object queryUserInfo(HttpServletRequest request, HttpServletResponse response) {
        return new Result(chatQuickBackService.queryAllTitle());
    }

    @PostMapping("/levitateButton")
    public Object queryLevitateButton(HttpServletRequest request, HttpServletResponse response) {
        return levitateButtonService.queryButtonList();
    }

    @PostMapping("/telegramConfig")
    public Object telegramConfig(HttpServletRequest request, HttpServletResponse response) {
        return companyConfigService.telegramConfig();
    }

    @PostMapping("/chatRule")
    public Object chatRule(HttpServletRequest request, HttpServletResponse response) {
        return companyConfigService.chatRule();
    }



}
