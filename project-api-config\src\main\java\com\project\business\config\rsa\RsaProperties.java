package com.project.business.config.rsa;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
@ConfigurationProperties(prefix = "third.company",ignoreInvalidFields = true)
public class RsaProperties implements Serializable {
    private static final long serialVersionUID = 7763603372889690538L;

    private String privateKey;

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }
}
