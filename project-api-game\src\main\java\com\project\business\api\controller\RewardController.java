package com.project.business.api.controller;


import com.project.business.service.game.RewardService;
import com.project.business.service.game.vo.RewardBaseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@RestController
@RequestMapping("/apiGame")
public class RewardController {



    @Autowired
    private RewardService rewardService;

    @PostMapping("/reward")
    public Object reward(HttpServletRequest request, HttpServletResponse response,  @RequestBody @Valid RewardBaseVo reqVo) {
        return rewardService.reward(reqVo);
    }
}
