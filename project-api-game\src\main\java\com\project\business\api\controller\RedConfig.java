package com.project.business.api.controller;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
 
@Component
@Data
@ConfigurationProperties(prefix = "red")
public class RedConfig {

    private List<String> display;
 
    public List<String> getDisplayValues() {
        return display;
    }
}