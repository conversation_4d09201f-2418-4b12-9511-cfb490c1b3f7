package com.project.business.api.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.project.business.api.service.PlatUserBalanceService;
import com.project.business.api.service.vo.OpenBalanceVo;
import com.project.business.config.balance.BalanceInfo;
import com.project.business.config.balance.BalanceResponse;
import com.project.business.config.balance.BalanceUrlConfig;
import com.project.business.customer.dao.FormListDao;
import com.project.business.customer.model.FormList;
import com.project.business.service.ImService;
import com.project.business.service.PlatBalanceService;
import com.project.framework.async.ExceptionHandlingAsyncTaskExecutor;
import com.project.framework.common.util.StrUtil;
import com.project.framework.producer.model.BalanceMessage;
import com.project.framework.producer.model.PushUpdateThirdAccountBalanceVo;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redis.RedisKeyPrefix;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class PlatUserBalanceServiceImpl implements PlatUserBalanceService {
    
    private static final Logger logger = LoggerFactory.getLogger(PlatUserBalanceServiceImpl.class);
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private FormListDao formListDao;
    @Autowired
    private PlatBalanceService platBalanceService;
    @Autowired
    private ImService imService;
    @Autowired
    private ExceptionHandlingAsyncTaskExecutor taskExecutor;
    
    @Override
    public BalanceInfo openBalance(OpenBalanceVo reqVo) {
        redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + reqVo.getUserId(),
                reqVo.getSessionId(), 10);
        
        BalanceMessage message = new BalanceMessage();
        //查询接口
        List<FormList> list = formListDao.queryByWindowCode("SpeakLimit", reqVo.getParentPlat());
        if (CollectionUtils.isEmpty(list)) {
            logger.info("当前平台没有相关的组件====platId:" + reqVo.getPlatId() + "=======parentPlat:" + reqVo.getParentPlat());
            return new BalanceInfo(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO);
        }
        FormList formList = list.get(0);
        BalanceUrlConfig redUrlConfig = JSONObject
                .toJavaObject(JSONObject.parseObject(formList.getFormJson()), BalanceUrlConfig.class);
        Map<String, Object> params = new HashMap<>();
        params.put("sessionId", reqVo.getSessionId());
        params.put("domain", reqVo.getDomain());
        params.put("method", reqVo.getMethod());
        String url = StrUtil.processTemplate(redUrlConfig.getApiUrl(), params);
        
        BalanceResponse balanceResponse = platBalanceService.ApplyBalance(url);
        BalanceInfo balanceInfo = balanceResponse.getBalanceInfo();
        BeanUtils.copyProperties(reqVo, message);
        message.setUrl(url);
        if (redisClient.exists(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + reqVo.getUserId())) {
            String sessionId = (String) redisClient
                    .get(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + reqVo.getUserId());
            if (!StringUtils.equals(sessionId, reqVo.getSessionId())) {
                redisClient.delete(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + sessionId);
            }
        }
        redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + reqVo.getUserId(),
                reqVo.getSessionId(), 7200);
        redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.PLAT_USER_MESSAGE + reqVo.getUserId(), message, 7200);
        redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + reqVo.getSessionId(), balanceInfo,
                7200);
        redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.QUARTZ_PLAT_BALANCE + reqVo.getUserId(),
                reqVo.getSessionId(), 60);
        
        return balanceInfo;
    }
    
    @Override
    public void startBalance(OpenBalanceVo reqVo) {
        taskExecutor.execute(() -> openBalance(reqVo));
    }
    
    @Override
    public void runMessage(String userId) {
        if (!redisClient.exists(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + userId)
                || !redisClient.exists(RedisKeyPrefix.BUSINESS, RedisKeyConstant.PLAT_USER_MESSAGE + userId)) {
            return;
        }
        BalanceMessage message = (BalanceMessage) redisClient
                .get(RedisKeyPrefix.BUSINESS, RedisKeyConstant.PLAT_USER_MESSAGE + userId);
        String sessionId = (String) redisClient.get(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + userId);
        BalanceInfo balanceInfoOld = (BalanceInfo) redisClient
                .get(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + sessionId);
        
        BalanceResponse balanceResponse = platBalanceService.ApplyBalance(message.getUrl());
        if (balanceResponse.getCode() != 200L) {
            logger.info("=============查询余额失败=========" + balanceResponse.getMessage());
            redisClient.delete(RedisKeyPrefix.BUSINESS, RedisKeyConstant.QUARTZ_PLAT_BALANCE + userId);
            redisClient.delete(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + sessionId);
            redisClient.delete(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + userId);
            redisClient.delete(RedisKeyPrefix.BUSINESS, RedisKeyConstant.PLAT_USER_MESSAGE + userId);
            return;
        }
        BalanceInfo balanceInfo = balanceResponse.getBalanceInfo();

        if ((balanceInfo != null && balanceInfoOld != null) &&
                (balanceInfoOld.getBalance().compareTo(balanceInfo.getBalance()) != 0 ||
                        balanceInfoOld.getDayBetting().compareTo(balanceInfo.getDayBetting()) != 0 ||
                        balanceInfoOld.getDayRecharge().compareTo(balanceInfo.getDayRecharge()) != 0)) { //余额改变
            logger.info("旧余额数据::" + JSONObject.toJSONString(balanceInfoOld));
            logger.info("新余额数据::" + JSONObject.toJSONString(balanceInfo));
            PushUpdateThirdAccountBalanceVo vo = new PushUpdateThirdAccountBalanceVo();
            vo.setPlatID(message.getPlatId().toString());
            vo.setUserID(message.getUserId().toString());
            vo.setRechargeAmount(balanceInfo.getDayRecharge().toPlainString());
            vo.setConsumptionAmount(balanceInfo.getDayBetting().toPlainString());
            vo.setBalanceAmount(balanceInfo.getBalance().toPlainString());
            imService.sendMessage(vo);
            redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.LOGIN_PLAT_USER + sessionId, balanceInfo, 7200);
        }
        redisClient.set(RedisKeyPrefix.BUSINESS, RedisKeyConstant.QUARTZ_PLAT_BALANCE + userId, sessionId, 60);
    }
    
}
