spring:
  application:
    name: apiBalance
  redis:
    data-base: 3
    host: ********
    port: 63795
    password: jhd83sdfde#ssS
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ************************************************************************************************************************************************************************************************************************ # 数据库地址
    username: user_java # 数据库用户名
    password: A8d#4kL!2mQ # 数据库密码
    driver-class-name: com.mysql.cj.jdbc.Driver # 数据库驱动
    type: com.zaxxer.hikari.HikariDataSource
    minimum-idle: 5
    maximum-pool-size: 500
    auto-commit: true
    idle-timeout: 30000
    max-lifetime: 100000
    connection-timeout: 40000
    connection-test-query: SELECT 1
    validation-timeout: 30000
    time-between-eviction-runs-millis: 300000
server:
  port: 9600
redisson: # 分布式事物锁redis配置
  address: redis://********:63795
  data-base: 2
  password: jhd83sdfde#ssS
  timeout: 30000
im:
  url: http://*********:8090/