package com.project.business.config.base;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Map;

@Setter
@Getter
public class ExtendConfig implements Serializable {
    
    private static final long serialVersionUID = 2558635434001749366L;
    @JSONField(defaultValue = "扩展限定条件:{ 'DAILY_RECHARGE':'日充值额度', 'DAY_BETTING':'日投注额度' }")
    protected Map<String, Object> extend;
}
