package com.project.business.config.balance;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class BalanceInfo implements Serializable {
    
    private static final long serialVersionUID = 3588946754986445170L;
    @JSONField(defaultValue = "用户余额")
    private BigDecimal balance;
    @JSONField(defaultValue = "日充值金额")
    private BigDecimal dayRecharge;
    @JSONField(defaultValue = "日投注金额")
    private BigDecimal dayBetting;
    
    public BalanceInfo(BigDecimal balance, BigDecimal dayRecharge, BigDecimal dayBetting) {
        this.balance = balance;
        this.dayRecharge = dayRecharge;
        this.dayBetting = dayBetting;
    }
}
