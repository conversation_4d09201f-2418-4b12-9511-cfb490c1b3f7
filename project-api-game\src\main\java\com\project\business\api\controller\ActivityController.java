package com.project.business.api.controller;

import com.project.business.service.game.ActivityService;
import com.project.business.service.game.vo.ActivityCenterEventVo;
import com.project.framework.aop.annotation.EscapeWrapper;
import com.project.framework.common.util.IpRemote;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 活动控制器
 */
@RestController
@RequestMapping("/apiGame")
public class ActivityController {

    @Autowired
    private ActivityService activityService;

    @EscapeWrapper
    @PostMapping("/activityCenterEvent")
    public Object activityCenterEvent(HttpServletRequest request, HttpServletResponse response,
                                      @RequestBody @Valid ActivityCenterEventVo reqVo) throws Exception {
        //获取用户ip
        String userIp = IpRemote.getIpAddr(request);
        return activityService.activityCenterEvent(reqVo, userIp);
    }
}
