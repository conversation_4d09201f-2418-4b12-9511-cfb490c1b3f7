package com.project.business.config.bet;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.MessageEnum;
import com.project.framework.core.base.BaseVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;

/**
 * 弹幕消息
 *
 * <AUTHOR>
 * @date 2020/03/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BarrageVo extends BaseVo {
    
    private static final long serialVersionUID = -2820572218895453766L;
    @JSONField(defaultValue = "")
    private String userId;
    /**
     * 弹幕目标群组id
     */
    private String[] groupIds;
    @NotEmpty(message = "vo.msgBody")
    private BarrageMessage[] msgBody;

    @NotNull(message = "vo.msgType")
    private MessageEnum msgType;

    @JSONField(defaultValue = "")
    private String msgStyle;

    @JSONField(defaultValue = "3")
    private Integer pollingCount;

    @JSONField(defaultValue = "10000")
    private Integer periodMillSecond;

    @NotBlank(message = "vo.companyId")
    private String companyId;
}
