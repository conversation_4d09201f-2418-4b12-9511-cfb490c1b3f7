package com.project.business.config.web;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.BaseConfig;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class WebConfig extends BaseConfig {
    
    private static final long serialVersionUID = 6323740461355881714L;
    @JSONField(defaultValue = "Window Title", name = "WindowTitle", ordinal = 1)
    private String windowTitle;
    @JSONField(defaultValue = "Window PC Url", name = "WindowUrlPC", ordinal = 2)
    private String windowUrlPc;
    @JSONField(defaultValue = "Window APP Url", name = "WindowUrlApp", ordinal = 3)
    private String windowUrlApp;
    @JSONField(defaultValue = "Window APP Method", name = "WindowMethodApp", ordinal = 4)
    private String windowMethodApp;
    @JSONField(defaultValue = "Window APP Param", name = "WindowParamApp", ordinal = 5)
    private String windowParamApp;
    @JSONField(defaultValue = "Event Method Name", name = "EventMethodName", ordinal = 6)
    private String eventMethodName;
    @JSONField(defaultValue = "Event Method Param", name = "EventMethodParam", ordinal = 7)
    private String eventMethodParam;

}
