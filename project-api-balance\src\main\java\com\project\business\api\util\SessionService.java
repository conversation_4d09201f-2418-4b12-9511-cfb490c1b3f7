package com.project.business.api.util;

import com.project.framework.common.service.ISessionService;
import org.springframework.stereotype.Service;

@Service
public class SessionService implements ISessionService {
    
    @Override
    public Long getUserId() {
        return 0L;
    }
    
    @Override
    public String getUserName() {
        return "SYS";
    }
    
    @Override
    public void refresh() {
    }
    
    @Override
    public Long getPlatId() {
        return 0L;
    }
    
    @Override
    public Long getParentPlat() {
        return null;
    }
    
    @Override
    public Integer getSysFlag() {
        return 1;
    }
    
    @Override
    public Integer getUserFlag() {
        return null;
    }
    
    @Override
    public Integer[] getRoleFlag() {
        return new Integer[0];
    }

    @Override
    public String getLanguage() {
        return null;
    }
}
