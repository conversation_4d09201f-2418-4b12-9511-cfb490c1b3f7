package com.project.business.config.bet;

import com.alibaba.fastjson.annotation.JSONField;
import com.project.business.config.base.MessageEnum;
import com.project.framework.core.base.BaseVo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.EqualsAndHashCode;

/**
 * 分享消息
 *
 * <AUTHOR>
 * @date 2020/03/03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ShareVo extends BaseVo {
    
    private static final long serialVersionUID = -909513759502697706L;
    @NotBlank(message = "vo.userId")
    private String userId;
    /**
     * 分享群组id
     */
    @NotEmpty(message = "vo.groupList")
    private String[] groupIds;

    @NotNull(message = "vo.msgBody")
    private MessageContent msgBody;

    @NotNull(message = "vo.msgType")
    private MessageEnum msgType;

    @JSONField(defaultValue = "")
    private String msgStyle;

    @NotBlank(message = "vo.companyId")
    private String companyId;
}
