package com.project.business.config.base;

public enum MessageEnum {
    
    BET_PLAN("投注计划", "BET"),
    BET_FOLLOW("跟投", "FOLLOW"),
    BARRAGE("弹幕", "BARRA<PERSON>"),
    SHARE("分享", "SHARE"),
    RED_ENVELOPE("红包", "ENVELOPE"),
    GROUP_TIP("群内信息", "GROUP_TIP");
    private String label;
    private String className;
    
    MessageEnum(String label, String className) {
        this.label = label;
        this.className = className;
    }
    
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
}
