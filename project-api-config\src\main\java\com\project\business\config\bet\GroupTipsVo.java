package com.project.business.config.bet;

import com.project.business.config.base.MessageEnum;
import com.project.framework.core.base.BaseVo;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 弹幕消息
 *
 * <AUTHOR>
 * @date 2020/07/01
 */
@Getter
@Setter
public class GroupTipsVo extends BaseVo {
    
    private static final long serialVersionUID = 4719687014833771153L;
    @NotNull(message = "vo.placeId")
    private Long groupId;
    @NotNull(message = "vo.msgBody")
    private GroupTipBody msgBody;
    @NotNull(message = "vo.msgType")
    private MessageEnum msgType;
    @NotBlank(message = "vo.companyId")
    private String companyId;
    private String msgStyle;
}
