package com.project.business.api.filter;

import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class ApiInterceptor extends HandlerInterceptorAdapter {
    
    @Override
    public boolean preHandle(HttpServletRequest httpRequest, HttpServletResponse httpResponse, Object handler)
            throws Exception {
//        httpResponse.addHeader("Access-Control-Allow-Origin", "*");
        httpResponse.addHeader("Access-Control-Allow-Methods", "POST,GET,OPTIONS");
        httpResponse.addHeader("Access-Control-Max-Age", "100");
        httpResponse.addHeader("Access-Control-Allow-Headers",
                "content-type,authorization,x-requested-with,userId,Authorization");
        httpResponse.addHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setContentType("application/json; charset=utf-8");
        return super.preHandle(httpRequest, httpResponse, handler);
    }
    
}
