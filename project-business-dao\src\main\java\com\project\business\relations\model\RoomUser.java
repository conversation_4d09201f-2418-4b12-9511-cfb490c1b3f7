package com.project.business.relations.model;

import com.project.framework.database.base.BaseModel;
import lombok.Getter;
import lombok.Setter;


/**
 * 群组用户信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-02 13:45:11
 */
@Getter
@Setter
public class RoomUser extends BaseModel{

	private static final long serialVersionUID = 3702308053957983651L;
	/**
	 * 房间编号
	 */
	private Long roomId;

	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 群管理编码
	 */
	private Long roleId;

	/**
	 * 群昵称
	 */
	private String remark;

	/**
	 * 审核或邀请user_id
	 */
	private Long chkUser;

	/**
	 * 用户状态
	 */
	private Integer status;

	/**
	 * 用户扩展状态属性
	 */
	private String extStatus;

	private String nickName;

	private String userName;

	/**
	 * 判断用户是否已经加入违禁词白名单
	 * 0未加入 , 1已加入
	 */
	private Integer whiteFlag = 0;


	public RoomUser(){

	}

	public RoomUser(Long roomId, Long platId, Long userId, Long roleId, Long chkUser){
		this.roomId = roomId;
		this.platId = platId;
		this.userId = userId;
		this.roleId = roleId;
		this.chkUser = chkUser;
	}


}
