package com.project.business.api.controller.live;

import com.project.business.service.merchant.MerchantLoginService;
import com.project.business.service.merchant.vo.MerchantCheckVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiIm")
public class LiveLoginController {
    
    @Autowired
    private MerchantLoginService merchantLoginService;
    
    @PostMapping("/liveCheck")
    public Object merchantCheck(HttpServletRequest request, HttpServletResponse response,
                                @RequestBody @Valid MerchantCheckVo reqVo) throws Exception {
        return merchantLoginService.liveCheck(reqVo);
    }
}
