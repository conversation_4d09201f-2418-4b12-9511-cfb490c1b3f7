100=Error userName or password
101=Save failed because of the data duplicate
102=Select a group please
103=Some members do not exist
104=Failed to obtain signature
105=Failed to get data
106=Please select the type of friend search
107=Please enter numeric member ID
108=Error of the window configuration format
109=Exist the same index
110=Unable to create chat group by current level
111=The formType reach maximum
112=Unable to create chat room by current level
113=Exist the same index
114=The admin is using and cannot operate
115=Blacklist cannot be empty
116=The session has been checked
117=The session does not exist
118=The account exists, please entry again
119=Only return the order status to [Pending payment]
120=The status of the paid order can only be changed to [Scored]
121=The domain cannot be empty
122=The domain does not exist
123=The platform does not exist or the domain is not configured
124=The chat group is disable
125=The selected member is abnormal
126=Imported members has reached maximum
127=The chat group has no admin can be setting
128=Number of the admin Has reached maximum
129=The chat group cannot set this admin
130=The chat room has no admin can be setting
131=The chat room cannot set this admin
132=Component does not exist or is disable
133=Unified collection has been opened,no permission operation
134=Private chat can only send one gift packet
135=The gift packet is invalid
136=Please contact the admin to configure the game information
137=The user\u2019s vIP level is too low to create chat group
138=The level of chat groups amount has reached maximum, please contact admin
139=Duplicate group members
140=Group members has reached maximum
141=The chat group cannot join members
142=Only the creator of the chat group can disband the group
143=User id cannot be empty
144=User login information cannot be empty
145=Wrong sharing type
146=Failed to get shared information
147=The class does not exist or is not serialized
148=The group level is using and cannot operate
149=The setting has exceeded the maximum number of user
150=The exported data cannot exceed 1000
151=The exported data cannot be empty
152=Please select start time and end time for customized time
153=The order number is abnormal
154=Username has been used
155=Sharing content is illegal
156=User does not exist or is disable
157=Cannot join the group repeatedly
158=Owning platform cannot be empty
159=The merchant has been configured
160=Unable to generate platform information
161=Please save the server configuration parameters first
162=The platform cannot create company
163=The same 'ThirdCompanyId' already exists in the current platform
164=The remaining number of current platform branches is insufficient
165=The current platform cannot create a merchant
166=The current platType has been use and cannot be operated
167=Error of the old password
168=Error amount and quantity of gift packet
169=File is too large
170=Data is empty, save failed
171=The language code is wrong or the current language is not supported temporarily

200=Successful
3300=Error email
2100=Exist the loginName
2200=Not exist the loginName or error password
2201=Please entry google code
2202=Error google code
2203=Bound google code
2204=Expired the google code
2205=The phone number registered
2206=The phone number not registered yet
2207=Graphic code has expired
2208=Graphic code error
2209=Too frequently to sending codes
2210=The code Has expired,Please reacquire
2211=The code Is invalid,Please reacquire

3400=Fail verify token,SignIn again please
4500=Error apply status

3501=Not exist the user or not the same platform
3502=The other party refuse

4001=Error appId
4002=Fail to get authorize
4003=The platform Has been disabled
4004=Should configure the friend link first
4005=Not exist the company
4006=The company Has been disabled
4007=Fail to get third_company_id
4008=this platform does not configure domain
4009=Fail to share
4010=Fail to calling service interface
4011=Not exist the chat
4012=The chat Has been disabled
4013=The chat unbound gift packet
4014=The companyId cannot be empty

4100=Error for group admin information

5001=Error for the platform
5002=Fail to decrypting for parameter
5003=Fail to verifying sign
5004=TimeOut for sign
5005=Too frequently to calling service interface
5100=The user Has been disabled
5101=The user Has no the authority

6001=It Is timeOut for login token
6002=It Is timeOut for share token

6010=The module uRL cannot be null
6011=The module Has not configure uRL
6012=The module Has been disabled

7001=Blank data for service interface


8001=Single gift packet can not lessThan 0.01
8002=Red packet can not moreThan oneMillion
8003=Fail send gift packet
8004=Error parameter
8005=Red packet Has been canceled
8006=Entry wallet password please
8007=Cannot get own gift packet
8008=Fail to calling wallet

9010=Failure
9001=Error userType!
9002=Not exist the order!
9003=Do not repeat!Pay first please!
9004=Do not repeat!
9005=It cannot be used,Please contact customer service!
9006=Recharge amount cannot lessThan 1
9100=Fail calling,Please try again later!
9102=Fail for order data
9103=Accounts cannot been used
9104=Merchant config cannot be used
9105=Merchant balance Is insufficient
9106=Merchant message template cannot be used
9107=Not exist the user
9108=Not exist the session
9109=Do not repeat!
9110=Expired login information!
9111=Error data format,Unresolved
9112=Fail to add daily data
9113=Company configuration charges are not available
9114=Insufficient company balance
9115=Abnormal company billing data
9116=The current scheme has been used and cannot be operated
9117=The company configuration charge already exists, there cannot be multiple valid configurations for one company at the same time
9118=Company configuration charges are not available, and the settlement type is not supported
9119=Company configuration has expired
9120=The company configuration is unavailable, and the associated scheme was not found
9121=The company configuration is unavailable, and the calculation fee should be at least greater than or equal to the guarantee limit
9122=The user's recharge amount is abnormal and needs to be greater than 1, but less than 1 million
9125=The exchange rate has not been configured for the current payment, Please contact customer service
9126=Did not match the bank merchants that meet the quota

7500=Error title of the Imported table
7501=Blank of the Imported table
7502=Blank of the Imported table content
7503=Error content format
7504=Error forbiddenType
7505=Error forbiddenState
7506=Error IP format
7507=Blank ID/IP
7508=Error format of Imported ID
7509=Not exist the Imported user
7510=The customer service Is unable to serve you
9000=Error parameter
9900=Error jSON format
9999=The system Is busy,Please try again later

7518=Cannot push on the current platform
7519=Message push failed
7520=The user cannot push messages

####################Vo parameter verification###########################
vo.parentPlat=ParentPlat must not be null
vo.platId=PlatId must not be null
vo.userId=UserId must not be null
vo.nickName=User nickname must not be blank
vo.sessionId=SessionId must not be blank
vo.formId=FormId must not be null
vo.placeId=Chat group or room Id must not be null
vo.placeId.error=Error of chat ID
vo.url=url must not be blank
vo.groupId=Chat group ID must not be null
vo.groupName=The chat group name must not be blank
vo.iconUrl=The chat group IconUrl must not be blank
vo.groupLevel=The groupLevel must not be null
vo.memberList=Please select members first
vo.group.remark=Group nickname must not be blank
vo.loginToken=Login token must not be blank
vo.serverNo=The serverNo must not be blank
vo.companyId=The companyId must not be blank
vo.phone=The phone number must not be blank
vo.phone.format=Error of the phone number format
vo.verCode=The graphic code must not be blank
vo.smsType=The sMS type must not be null
vo.smsCode=The sMS code must not be blank
vo.account=account must not be blank
vo.account.format=The account must consist of 4-10 digits in english and numbers
vo.password=Password must not be blank
vo.smsToken=Illegal request, please re-register
vo.friendId=Friend ID must not be null
vo.redId=Red packet ID must not be null
vo.redType=Red packet type must not be null
vo.redAmount=Red packet amount must not be null
vo.redCount=Red packet quantity must not be null
vo.redCount.min=Please send at least one gift packet
vo.param.error=Error parameter
vo.authorization=Authorized authentication must not be blank
vo.userType=The userType must not be null
vo.userToken=User authorization must not be blank
vo.liveToken=Live authorization must not be blank
vo.live.sessionId=Live session ID must not be blank
vo.live.sessionHistory=The live session History must not be empty
vo.live.eventKey=Live event key must not be blank
vo.live.speakTime=Last speaking time cannot be null
vo.merchantToken=Merchant authorization must not be blank
vo.orderId=Merchant order ID must not be blank
vo.merchant.eventKey=Customer service event kEY must not be blank
vo.appId=AppId must not be blank
vo.appSecret=AppSecret must not be blank
vo.groupList=GroupList must not be empty
vo.msgType=MsgType must not be null
vo.msgBody=mMsgBody must not be null
vo.msgPrivate=MsgPrivate must not be null
vo.greetings=Greetings must not be blank
vo.scenes=Scenes must not be blank
vo.action=Action must not be blank
vo.result=Result must not be blank
vo.amount=amount must not be blank
vo.subject=Subject information must not be blank
vo.optionType=Operation type must not be null
vo.userList=User info must not be empty
vo.userList.max=The maximum number of users cannot exceed 1000
vo.platIds=Please choose a platform
vo.content=content must not be blank
vo.effectiveTime=Effective time must not be blank
vo.invalidTime=Invalid time must not be blank
vo.dataId=Primary key ID must not be null
vo.friendStatus=The code of friend search status must not be null
vo.friendStatus.error=The code of friend search status error
vo.header=The status of customer service Head must not be null
vo.header.error=The status of customer service Head error
vo.skin=Skin must not be blank
vo.privateChat=The private chat code must not be null
vo.privateChat.error=Error the private chat code
vo.merchantId=Please choose a merchant first
vo.merchant.configs=Merchant configuration must not be empty
vo.thirdUserName=The thirdUserName must not be null
vo.thirdPassword=The thirdPassword must not be null
vo.platName=Platform name  must not be blank
vo.platEmail=Platform email must not be blank
vo.platEmail.format=Error of platform email format
vo.platPhone=Platform phone must not be blank
vo.domainUrl=Platform domainUrl must not be blank
vo.platType=Platform type must not be null
vo.loginName=loginName must not be blank
vo.roleId=Role ID must not be null
vo.accountNos=Mute account must not be blank
vo.forbiddenType=Mute type must not be null
vo.formType=component type must not be null
vo.formIcon=Component Icon must not be blank
vo.formName=Component name must not be blank
vo.formIndex=Component index must not be null
vo.formWindow=Please select window first
vo.authFlag=Please select whether need to verify the visa first
vo.buttonName=Button name must not be blank
vo.buttonCode=Button code must not be blank
vo.buttonIndex=Button index must not be null
vo.enterLevel=VIP for chat room must not be null
vo.entryMode=Please select theWay to join chat group first
vo.entryMode.error=The selected way to join chat group Is wrong
vo.forbidMode=Please select the mute method first
vo.forbidMode.error=The selected mute method is wrong
vo.allowLink=Please choose whether to allow sending links
vo.allowLink.error=Allow send link code incorrect
vo.leadThirdIds=User ID must not be empty
vo.lowerId=Please select Import method
vo.group.managerId=Admin ID in group must not be null
vo.group.userIds=Members in selected group must not be empty
vo.placeRule.list=Component rules list must not be empty
vo.ruleId=Component rules ID must not be null
vo.ruleLimit=Component rules must not be empty
vo.bindFlag=Binding mark must not be null
vo.wordReplace=Prohibited word substitute must not be blank
vo.wordModel=Prohibited words model must not be null
vo.orderNum=Prohibited words index must not be null
vo.orderNum.error=Prohibited words index limit exceeded
vo.identityCode=Identity code must not be blank
vo.identityName=Identity name must not be blank
vo.identityIndex=Identity index must not be null
vo.identityIndex.error=Error of Identity index
vo.callbackUrl=Please enter the callback uRL first
vo.token=Please enter the token first
vo.token.error=Please enter the correct token
vo.encodingAesKey=Please enter the message encryption and decryption key (encodingAESKey)first
vo.encodingAesKey.error=Please eEnter the correct encodingAESKey
vo.roomId=Chat room ID must not be null
vo.roomName=Chat room name must not be blank
vo.room.managerId=Chat room manager must not be null
vo.openType=OpenType must not be null
vo.windowType=WindowType must not be null
vo.className=ClassName must not be null
vo.levelIndex=Chat group level index must not be null
vo.maxUser=Maximum number of users in the group must not be null
vo.typeId=Group type must not be null
vo.levelName=Group level name must not be blank
vo.managerIndex=Group manager index must not be null
vo.managerIndex.error=The minimum value is 100 of group manager index
vo.managerName=The group manager type name must not be blank
vo.blackList.type=The blacklist type must not be null
vo.blackList.list=The blacklist must not be empty
vo.blackList.idType=The ID type must not be null
vo.itemName=The evaluation item name must not be blank
vo.itemIndex=The evaluation item index must not be null
vo.metricsId=The metrics ID must not be null
vo.metricsList=The metrics must not be blank
vo.dateType=The date type must not be blank
vo.dateType.error=Error of date type
vo.quickBack.content=Quick reply content must not be blank
vo.plat.domainUrl=The domainUrl must not be blank
vo.typeName=Platform type name must not be blank
vo.companyNumber=Number of company must not be null
vo.companyNumber.min=Number of company cannot be less than 0
vo.companyNumber.max=Number of company cannot be greater than 2000
vo.functionList=Please select platform permission type first
vo.oldPasswd=Old password must not be blank
vo.newPasswd=New password must not be blank
vo.googleCode=Google code must not be blank

#############api\u5185\u8FD4\u56DE##############
api.java.redEnvelope.greetings=Best wishes
api.java.redEnvelope.lucky.subject=User {nickName} has been opened your gift packet\uFF1A{amount}
api.java.redEnvelope.redMark.expired.self=The gift packet is expired, It has been opened {grabNumber}/{redCount}, a total of {grabAmount}/{redAmount}{currency}
api.java.redEnvelope.redMark.expired=The gift packet is expired, It has been opened {grabNumber}
api.java.redEnvelope.redMark.excess=A total of {redCount} gift packets, {time} none left
api.java.redEnvelope.redMark.success.self=It has been opened {grabNumber}/{redCount}, a total of {grabAmount}/{redAmount}
api.java.redEnvelope.redMark.success=It has been opened {grabNumber}
api.java.day= day
api.java.hour= hour
api.java.min= minute
api.java.s= second
api.java.live.token.windowsTitle=SecurityChat Customer Service
api.java.place.admin.10001=SecurityChat Assistant
api.java.live.serverLogin.customer.userName=Visitor
api.java.enum.RedType.LUCKY=Lucky Gift Packet
api.java.enum.RedType.AVERAGE=Average Gift Packet

api.merchant.MerchantOrderServiceImpl.accountNo=Account
api.merchant.MerchantOrderServiceImpl.realName=FullName
api.merchant.MerchantOrderServiceImpl.IFSCCode=IFSC
api.merchant.MerchantOrderServiceImpl.remark=Postscript
api.merchant.MerchantOrderServiceImpl.bankAddress=Account Bank
api.merchant.MerchantOrderServiceImpl.protocolCode=Mainnet protocol
api.merchant.MerchantOrderServiceImpl.address=Receiving address
api.merchant.MerchantOrderServiceImpl.amount=order amount
api.merchant.MerchantOrderServiceImpl.rate=Exchange rate
api.merchant.MerchantOrderServiceImpl.number=Recharge quantity

api.merchant.MerchantOrderServiceImpl.upiAccount=UPI account number
api.merchant.MerchantOrderServiceImpl.upiWallet=UPI Wallet
api.merchant.MerchantOrderServiceImpl.upiName=Account name