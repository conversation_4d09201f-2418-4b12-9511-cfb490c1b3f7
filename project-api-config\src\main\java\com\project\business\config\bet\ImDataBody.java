package com.project.business.config.bet;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ImDataBody implements Serializable {
    
    private static final long serialVersionUID = 7153915012936217800L;
    /**
     * 消息发起人
     */
    @JSONField(name = "UserID")
    private Long userId;
    private String nickName;
    private String iconUrl;
    private String gameType;
    private String msgType;
    private String msgStyle;
    private MsgBody msgBody;
    private Integer replaceKey;

    public Integer getReplaceKey() {
        return this.replaceKey == null ? 1 : this.replaceKey;
    }

    public void setReplaceKey(Integer replaceKey) {
        if (replaceKey == null){
            replaceKey = 1;
        }
        this.replaceKey = replaceKey;
    }
}
