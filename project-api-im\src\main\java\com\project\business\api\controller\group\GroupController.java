package com.project.business.api.controller.group;

import com.project.business.dict.GroupType;
import com.project.business.manager.model.GroupLevel;
import com.project.business.service.bean.GroupLevelBean;
import com.project.business.service.group.GroupInfoService;
import com.project.business.service.group.vo.AddMemberVo;
import com.project.business.service.group.vo.GroupBaseVo;
import com.project.business.service.group.vo.GroupExtendVo;
import com.project.business.service.group.vo.GroupInfoVo;
import com.project.business.service.group.vo.GroupManagerVo;
import com.project.business.service.group.vo.PrivateChatVo;
import com.project.business.service.group.vo.QueryGroupUserVo;
import com.project.business.service.group.vo.UserRemarkVo;
import com.project.business.service.plat.PlatTypeService;
import com.project.framework.common.service.ISessionService;
import com.project.framework.core.code.Result;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.project.framework.redis.RedisKeyConstant;
import com.project.framework.redisson.RedissLockUtil;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/apiIm")
public class GroupController {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private GroupInfoService groupInfoService;
    @Autowired
    private PlatTypeService platTypeService;
    @Autowired
    private ISessionService sessionService;
    
    @PostMapping("/queryGroupInfo")
    public Object queryGroupInfo(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid GroupBaseVo reqVo) {
        return groupInfoService.queryOne(reqVo.getGroupId());
    }
    
    @PostMapping("/createGroup")
    public Object createGroup(HttpServletRequest request, HttpServletResponse response,
                              @RequestBody @Valid GroupInfoVo reqVo) {
        return groupInfoService.createGroup(reqVo);
    }
    
    @PostMapping("/queryGroupLevel")
    public Object queryGroupLevel(HttpServletRequest request, HttpServletResponse response) {
        final List<GroupLevel> list = platTypeService.queryGroupLevelByType(GroupType.GROUP_NORMAL);
        final List<GroupLevelBean> result = new ArrayList<>();
        if (list != null && list.size() > 0) {
            list.forEach(groupLevel -> {
                GroupLevelBean groupLevelBean = new GroupLevelBean();
                BeanUtils.copyProperties(groupLevel, groupLevelBean);
                result.add(groupLevelBean);
            });
        }
        return new Result(result);
    }
    
    @PostMapping("/queryManagerLevel")
    public Object queryManagerLevel(HttpServletRequest request, HttpServletResponse response,
                                    @RequestBody @Valid GroupBaseVo reqVo) {
        return new Result(groupInfoService.queryManagerLevel(reqVo.getGroupId()));
    }
    
    @PostMapping("/addGroupMember")
    public Object addGroupMember(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid AddMemberVo reqVo) {
        groupInfoService.addGroupMember(reqVo);
        return null;
    }
    
    @PostMapping("/queryGroupMember")
    public Object queryGroupMember(HttpServletRequest request, HttpServletResponse response,
                                   @RequestBody @Valid QueryGroupUserVo reqVo) {
        return groupInfoService.queryGroupMember(reqVo);
    }
    
    
    @PostMapping("/removeGroupMember")
    public Object removeGroupMember(HttpServletRequest request, HttpServletResponse response,
                                    @RequestBody @Valid AddMemberVo reqVo) {
        //移除群组成员
        groupInfoService.removeGroupMember(reqVo);
        return null;
    }
    
    @PostMapping("/updateGroupUserRemark")
    public Object updateGroupUserRemark(HttpServletRequest request, HttpServletResponse response,
                                        @RequestBody @Valid UserRemarkVo reqVo) {
        groupInfoService.updateGroupUserRemark(reqVo);
        return null;
    }
    
    @PostMapping("/setGroupManager")
    public Object setGroupManager(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody @Valid GroupManagerVo reqVo) {
        groupInfoService.setGroupManager(reqVo);
        return null;
    }
    
    @PostMapping("/removeGroupManager")
    public Object removeGroupManager(HttpServletRequest request, HttpServletResponse response,
                                     @RequestBody @Valid GroupManagerVo reqVo) {
        groupInfoService.removeGroupManager(reqVo);
        return null;
    }
    
    @PostMapping("/updateGroupInfo")
    public Object updateGroupInfo(HttpServletRequest request, HttpServletResponse response,
                                  @RequestBody @Valid GroupExtendVo reqVo) {
        groupInfoService.updateGroupInfo(reqVo);
        return null;
    }
    
    @PostMapping("/disbandGroup")
    public Object disbandGroup(HttpServletRequest request, HttpServletResponse response,
                               @RequestBody @Valid GroupBaseVo reqVo) {
        //群组解散
        groupInfoService.disbandGroup(reqVo);
        return null;
    }
    
    @PostMapping("/privateChat")
    public Object getPrivateChat(HttpServletRequest request, HttpServletResponse response,
                                 @RequestBody @Valid PrivateChatVo reqVo) {
        logger.info("接受到请求 ： {}" , System.currentTimeMillis());
        final Long userId = sessionService.getUserId();
        final Long friendId = reqVo.getFriendId();
        final String key = RedisKeyConstant.PRIVATE_CHAT_ID + userId + ":" + friendId;
        final String key2 = RedisKeyConstant.PRIVATE_CHAT_ID + friendId + ":" + userId;
        logger.info("即将上锁 ： {}" , System.currentTimeMillis());
        RLock lock = RedissLockUtil.multiLock(120, key, key2);
        try {
            logger.info("上锁成功 ： {}" , System.currentTimeMillis());
            return groupInfoService.privateChatId(reqVo);
        }finally {
            logger.info("开始解锁 ： {}" , System.currentTimeMillis());
            if(lock!=null){
                lock.unlock();
                logger.info("解锁成功 ： {}" , System.currentTimeMillis());
            }
        }

    }
    
}
