package com.project.business.config.activity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ActivityResponse implements Serializable {
    private static final long serialVersionUID = -1348393290020705683L;

    @JSONField(name = "Status")
    private boolean status;

    @JSONField(name = "Data")
    private String data;

    @JSONField(name = "Code")
    private Integer code;

    @JSONField(name = "Info")
    private String info;

    @JSONField(name = "MultiLangCode")
    private Object multiLangCode;
}
