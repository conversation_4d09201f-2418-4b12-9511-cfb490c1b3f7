package com.project.business.api.controller.live;


import com.project.business.service.live.LiveStreamingCallbacksService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("/apiChat")
public class LiveStreamingCallbacksController {



    @Resource
    private LiveStreamingCallbacksService liveStreamingCallbacksService;


    @GetMapping("/live/callbacks")
    public Object streamPushCallback(HttpServletRequest request) {
        return liveStreamingCallbacksService.streamPushCallback(request);
    }






}
