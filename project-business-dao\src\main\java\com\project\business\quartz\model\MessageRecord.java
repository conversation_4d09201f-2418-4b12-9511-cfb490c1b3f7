package com.project.business.quartz.model;

import com.project.framework.database.base.BaseModel;
import com.project.business.dict.MsgType;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 
 * <br>消息监听记录表
 * <b>功能：</b>MessageRecordEntity<br>
 * <AUTHOR>
 * @version 1.0
 * @date ${date}
 */
@Getter
@Setter
public class MessageRecord extends BaseModel {


	private static final long serialVersionUID = -2762862570124747732L;
	//消费完成时间
	private Date consumTime;
	//消费端
	private String consumer;
	//加密标记 0:不加密  1.加密
	private  Integer encryFlag;
	//消息内容
	private String messageBody;
	//消息对象类
	private String messageClass;
	//消息ID
	private String messageId;
	//消息状态 1:待确认  2.已确认 3.处理完成
	private Integer messageStatus;
	//消息类型
	private MsgType messageType;
	//通知次数
	private Long noticeNumber;
	//上次通知时间
	private Date noticeTime;
	//通知地址
	private String noticeUrl;
	//生产端
	private String producer;
}

