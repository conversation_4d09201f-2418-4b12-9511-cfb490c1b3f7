package com.project.business.api;

import com.project.business.customer.dao.RoomInfoDao;
import com.project.business.customer.model.RoomInfo;
import com.project.framework.redis.RedisClient;
import com.project.framework.redis.RedisKeyConstant;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CacheLoaderService implements ApplicationRunner {



    @Resource
    private RoomInfoDao roomInfoDao;
    @Resource
    private RedisClient redisClient;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        List<RoomInfo> roomInfos = roomInfoDao.queryByList(null);
        for (RoomInfo roomInfo : roomInfos) {
            Long platId = roomInfo.getPlatId();
            Long roomId = roomInfo.getRoomId();
            String key = RedisKeyConstant.CHATROOM_ONLINE_STATS+platId+":"+roomId;
            redisClient.addToSet(key,"__empty__");
//            int currentHour = DateUtil.hour(DateUtil.date(), true);
//            String countKey = RedisKeyConstant.CHATROOM_ONLINE_STATS_COUNT+platId+":"+roomId+":"+currentHour;
//            redisClient.setValue(countKey,1);
        }
        System.out.println("Cache loaded successfully using ApplicationRunner");
    }
}
