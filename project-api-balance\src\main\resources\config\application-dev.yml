spring:
  application:
    name: apiBalance
  redis:
    data-base: 3
    host: *********
    port: 63795
    password: jhd83sdfde#ssS
    lettuce:
      pool:
        max-wait: 30000ms
        max-idle: 50
        max-active: 200
        min-idle: 10
    timeout: 30000ms
  datasource:
    url: ***************************************************************************************************************************************************************************************************
    username: admin
    password: kfukjijfioeww2!
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    minimum-idle: 5
    maximum-pool-size: 200
    auto-commit: true
    idle-timeout: 30000
    pool-name: DatebookHikariCP
    max-lifetime: 100000
    connection-timeout: 10000
    connection-test-query: SELECT 1
server:
  port: 9600
redisson:
  address: redis://*********:63795
  data-base: 1
  password: jhd83sdfde#ssS
  timeout: 30000
im:
  url: http://*********:9100/