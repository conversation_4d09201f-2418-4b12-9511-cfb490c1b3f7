package com.project.business.relations.dao;

import com.project.business.relations.model.GroupUser;
import com.project.business.relations.model.RoomUser;
import com.project.framework.database.base.BaseDao;

import java.util.List;
import java.util.Map;

/**
 * 群组用户信息表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-12-02 13:45:11
 */
public interface RoomUserDao extends BaseDao {


    int batchAdd(List<RoomUser> userList);

    int updateRoleId(GroupUser groupUser);

    int deleteByGroupId(Long groupId);

    int createTemporaryTable(Map<String, Object> param);
    /**
     * 查询群组管理员列表
     * @param groupId
     * @return
     */
    List<GroupUser> queryManagerByGroupId(Long groupId);
}
