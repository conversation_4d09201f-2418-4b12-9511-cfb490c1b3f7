import path from 'node:path'
import fs from 'node:fs'

const files = fs.readdirSync(path.resolve('locales'))
const data = {}
const targetFile = path.resolve('../project-business-admin/src/main/resources/static/js/locales.js')

const fn = `
function $t(key, arr = []) {
    const defaultLocale = 'zh'
    const locale = localStorage.getItem('locale') || defaultLocale
    const data = LOCALES_DATA[locale] || LOCALES_DATA[defaultLocale]
    const t = (text) => text.replace(/\\{(\\d+)\\}/g, (match, index) => {
        return arr[index]    
    })

    if (key in data) {
        return t(data[key])
    } else {
        return t(key)
    }
}

function $$t() {
    document.querySelectorAll('[data-i18n]').forEach(el => {
        const key = el.dataset.i18n
        const arr = new Function('return ' + el.dataset.i18nArgs)() || []
        const text = $t(key, arr)
        el.textContent = text
    })
}

document.addEventListener('DOMContentLoaded', () => {
    $$t()
})
`
for (const file of files) {
    if (file.endsWith('.json')) {
        const lang = file.replace('.json', '')
        const content = fs.readFileSync(path.resolve('locales', file), 'utf-8')
        data[lang] = JSON.parse(content)
    }
}
fs.writeFileSync(targetFile, 'const LOCALES_DATA = ' + JSON.stringify(data, null, 2) + fn, 'utf-8')