{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "ApiBalanceApplication",
            "request": "launch",
            "mainClass": "com.project.business.api.ApiBalanceApplication",
            "projectName": "project-api-balance"
        },
        {
            "type": "java",
            "name": "ApiChatApplication",
            "request": "launch",
            "mainClass": "com.project.business.api.ApiChatApplication",
            "projectName": "project-api-chat"
        },
        {
            "type": "java",
            "name": "ApiGameApplication",
            "request": "launch",
            "mainClass": "com.project.business.api.ApiGameApplication",
            "projectName": "project-api-game"
        },
        {
            "type": "java",
            "name": "ApiImApplication",
            "request": "launch",
            "mainClass": "com.project.business.api.ApiImApplication",
            "projectName": "project-api-im"
        },
        {
            "type": "java",
            "name": "ApiOfficialApplication",
            "request": "launch",
            "mainClass": "com.project.business.api.ApiOfficialApplication",
            "projectName": "project-api-official"
        },
        {
            "type": "java",
            "name": "ApiPlatApplication",
            "request": "launch",
            "mainClass": "com.project.business.api.ApiPlatApplication",
            "projectName": "project-api-plat"
        },
        {
            "type": "java",
            "name": "admin",
            "request": "launch",
            "mainClass": "com.project.business.admin.ProjectStartApplication",
            "projectName": "project-business-admin"
        },
        {
            "type": "java",
            "name": "MQApplication",
            "request": "launch",
            "mainClass": "com.project.framework.consumer.MQApplication",
            "projectName": "project-business-consumer"
        },
        {
            "type": "java",
            "name": "ProjectBusinessPayApplication",
            "request": "launch",
            "mainClass": "com.project.framework.pay.ProjectBusinessPayApplication",
            "projectName": "project-business-pay"
        },
        {
            "type": "java",
            "name": "QuartzApplication",
            "request": "launch",
            "mainClass": "com.project.business.quartz.QuartzApplication",
            "projectName": "project-business-quartz"
        },
        {
            "type": "java",
            "name": "SendUserPrizeListServiceImpl",
            "request": "launch",
            "mainClass": "com.project.business.service.game.impl.SendUserPrizeListServiceImpl",
            "projectName": "project-business-service"
        },
        {
            "type": "java",
            "name": "ArrayUtil",
            "request": "launch",
            "mainClass": "com.project.framework.common.util.ArrayUtil",
            "projectName": "project-framework-common"
        },
        {
            "type": "java",
            "name": "EncryptionUtil",
            "request": "launch",
            "mainClass": "com.project.framework.common.util.EncryptionUtil",
            "projectName": "project-framework-common"
        },
        {
            "type": "java",
            "name": "RSAUtil",
            "request": "launch",
            "mainClass": "com.project.framework.common.util.RSAUtil",
            "projectName": "project-framework-common"
        },
        {
            "type": "java",
            "name": "ApiDemoApplication",
            "request": "launch",
            "mainClass": "com.project.business.demo.ApiDemoApplication",
            "projectName": "project-merchant-demo"
        }
    ]
}