package com.project.business.api.controller.common;

import com.project.business.service.token.TokenService;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CommonController {

    @Autowired
    private TokenService tokenService;

    @RequestMapping("/checkToken")
    public Object checkToken(HttpServletRequest request) throws Exception {
        final String OAuthToken = request.getHeader("Authorization");
        if(StringUtils.isEmpty(OAuthToken)) {
            throw new GlobalException(Cause.error_user_token);
        }
        return tokenService.checkToken(OAuthToken);
    }

}
