package com.project.business.api.util;

import com.project.business.service.bean.TokenMsg;

public class ApiThreadLocal {
    
    private static final ThreadLocal<TokenMsg> local = new ThreadLocal<>();
    
    private ApiThreadLocal() {
    }
    
    public static void set(TokenMsg user) {
        local.set(user);
    }
    
    public static void setPlatId(Long platId) {
        local.get().setPlatId(platId);
    }
    
    public static void setParentId(Long parentId) {
        local.get().setParentId(parentId);
    }
    
    public static TokenMsg get() {
        return local.get();
    }
    
    public static void remove() {
        local.remove();
    }
}
