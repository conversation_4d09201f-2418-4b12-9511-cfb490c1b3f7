package com.project.business.api.controller.user;

import com.project.business.service.user.PlatUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/apiIm")
public class UserBalanceController {
    
    @Autowired
    private PlatUserService platUserService;
    
    @RequestMapping("/queryUserBalance")
    public Object queryUserBalance(HttpServletRequest request, HttpServletResponse response) {
        return platUserService.queryUserBalance();
    }
    
}
