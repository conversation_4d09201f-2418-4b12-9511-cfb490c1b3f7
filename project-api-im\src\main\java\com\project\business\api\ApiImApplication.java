package com.project.business.api;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.TimeZone;

@ComponentScan(basePackages = {"com.project"})
@Configuration
@EnableConfigurationProperties
@EnableScheduling
@EnableAutoConfiguration
public class ApiImApplication {
    
    private static final Logger log = LoggerFactory.getLogger(ApiImApplication.class);
    
    public static void main(String[] args) throws UnknownHostException {
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
        SpringApplication app = new SpringApplication(ApiImApplication.class);
        app.setBannerMode(Banner.Mode.CONSOLE);
        Environment env = app.run(args).getEnvironment();
        log.info("----------------------------------------------------------");
        log.info("Application '{}' is running! Access URLs:", env.getProperty("spring.application.name"));
        log.info("Local:  http://127.0.0.1:{} ", env.getProperty("server.port"));
        log.info("External:  http://{}:{} ", InetAddress.getLocalHost().getHostAddress(),
                env.getProperty("server.port"));
        log.info("Application '{}' is running! Access URLs:", env.getProperty("spring.application.name"));
        log.info("-------------------------12.17---------------------------------");
    }
}
