package com.project.business.api.filter;

import com.alibaba.fastjson.JSON;
import com.project.business.api.util.ApiThreadLocal;
import com.project.business.service.token.TokenService;
import com.project.framework.core.RespObject;
import com.project.framework.core.exception.Cause;
import com.project.framework.core.exception.GlobalException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class ApiInterceptor extends HandlerInterceptorAdapter {
    
    private final Logger log = LoggerFactory.getLogger(TokenInterceptor.class);
    @Autowired
    private TokenService tokenService;
    
    @Override
    public boolean preHandle(HttpServletRequest httpRequest, HttpServletResponse httpResponse, Object handler)
            throws Exception {
//        httpResponse.addHeader("Access-Control-Allow-Origin", "*");
        httpResponse.addHeader("Access-Control-Allow-Methods", "POST,GET,OPTIONS");
        httpResponse.addHeader("Access-Control-Max-Age", "100");
        httpResponse.addHeader("Access-Control-Allow-Headers",
                "content-type,authorization,x-requested-with,userId,Authorization");
        httpResponse.addHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setContentType("application/json; charset=utf-8");
        String method = httpRequest.getMethod();
        if (method.equalsIgnoreCase("OPTIONS")) {
            httpResponse.setStatus(200);
            httpResponse.getWriter().write(JSON.toJSONString(new RespObject()));
            return false;
        }
        final String OAuthToken = httpRequest.getHeader("Authorization");
        log.info("USER_TOKEN::" + OAuthToken);
        if (StringUtils.isEmpty(OAuthToken)) {
            throw new GlobalException(Cause.error_user_token);
        }
        
        ApiThreadLocal.set(tokenService.checkToken(OAuthToken));
        return super.preHandle(httpRequest, httpResponse, handler);
    }
    
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        ApiThreadLocal.remove();
        super.afterCompletion(request, response, handler, ex);
    }
    
}
